"use client";

import React, { useEffect } from "react";
import { Moon, Sun, RotateCcw } from "lucide-react";
import { useComponentStore } from "@/store/componentStore";
import { generateComponentCode } from "@/lib/codeGenerator";
import ComponentTypeSelector from "@/components/ui/ComponentTypeSelector";
import PreviewContainer from "@/components/ui/PreviewContainer";
import CodeBlock from "@/components/ui/CodeBlock";
import ComponentPreview from "@/components/ui/ComponentPreview";
import PresetManager from "@/components/ui/PresetManager";
import NavbarGenerator from "@/components/generators/NavbarGenerator";
import ButtonGenerator from "@/components/generators/ButtonGenerator";

export default function Home() {
  const {
    currentComponent,
    config,
    generatedCode,
    previewMode,
    darkMode,
    setCurrentComponent,
    setGeneratedCode,
    setPreviewMode,
    toggleDarkMode,
    resetConfig,
  } = useComponentStore();

  // Generate code whenever config changes
  useEffect(() => {
    const code = generateComponentCode(config);
    setGeneratedCode(code);
  }, [config, setGeneratedCode]);

  const renderGenerator = () => {
    switch (currentComponent) {
      case "navbar":
        return <NavbarGenerator />;
      case "button":
        return <ButtonGenerator />;
      case "card":
        return (
          <div className="p-4 text-center text-muted-foreground">
            Card generator coming soon...
          </div>
        );
      case "form":
        return (
          <div className="p-4 text-center text-muted-foreground">
            Form generator coming soon...
          </div>
        );
      default:
        return (
          <div className="p-4 text-center text-muted-foreground">
            Select a component type to get started
          </div>
        );
    }
  };

  const renderPreview = () => {
    return <ComponentPreview config={config} />;
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground">
                Component Generator
              </h1>
              <p className="text-muted-foreground mt-1">
                Create beautiful React components with Tailwind CSS
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={resetConfig}
                className="flex items-center space-x-2 px-3 py-2 text-sm bg-background border border-border rounded-md hover:bg-accent hover:text-accent-foreground transition-colors"
                title="Reset configuration"
              >
                <RotateCcw className="w-4 h-4" />
                <span className="hidden sm:inline">Reset</span>
              </button>

              <button
                onClick={toggleDarkMode}
                className="flex items-center space-x-2 px-3 py-2 text-sm bg-background border border-border rounded-md hover:bg-accent hover:text-accent-foreground transition-colors"
                title={
                  darkMode ? "Switch to light mode" : "Switch to dark mode"
                }
              >
                {darkMode ? (
                  <Sun className="w-4 h-4" />
                ) : (
                  <Moon className="w-4 h-4" />
                )}
                <span className="hidden sm:inline">
                  {darkMode ? "Light" : "Dark"}
                </span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left sidebar - Component selection and configuration */}
          <div className="lg:col-span-1 space-y-6">
            <ComponentTypeSelector
              value={currentComponent}
              onChange={setCurrentComponent}
            />

            <div className="bg-card border border-border rounded-lg p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4">
                Configuration
              </h3>
              {renderGenerator()}
            </div>

            <div className="bg-card border border-border rounded-lg p-6">
              <PresetManager />
            </div>
          </div>

          {/* Right content - Preview and code */}
          <div className="lg:col-span-2 space-y-6">
            <PreviewContainer mode={previewMode} onModeChange={setPreviewMode}>
              {renderPreview()}
            </PreviewContainer>

            <CodeBlock
              code={generatedCode}
              filename={`${currentComponent.charAt(0).toUpperCase() + currentComponent.slice(1)}.tsx`}
            />
          </div>
        </div>
      </main>
    </div>
  );
}
