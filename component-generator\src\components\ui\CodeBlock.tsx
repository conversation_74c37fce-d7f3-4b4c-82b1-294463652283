'use client';

import React, { useState } from 'react';
import { Copy, Download, Check } from 'lucide-react';
import { cn, copyToClipboard, downloadFile } from '@/lib/utils';

interface CodeBlockProps {
  code: string;
  language?: string;
  filename?: string;
  className?: string;
  showLineNumbers?: boolean;
}

export default function CodeBlock({ 
  code, 
  language = 'tsx', 
  filename = 'Component.tsx',
  className,
  showLineNumbers = true 
}: CodeBlockProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    const success = await copyToClipboard(code);
    if (success) {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handleDownload = () => {
    downloadFile(code, filename, 'text/plain');
  };

  const lines = code.split('\n');

  return (
    <div className={cn('relative bg-card border border-border rounded-lg overflow-hidden', className)}>
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-2 bg-muted border-b border-border">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-foreground">{filename}</span>
          <span className="text-xs text-muted-foreground uppercase">{language}</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleCopy}
            className="flex items-center space-x-1 px-2 py-1 text-xs bg-background border border-border rounded hover:bg-accent hover:text-accent-foreground transition-colors"
            title="Copy to clipboard"
          >
            {copied ? (
              <>
                <Check className="w-3 h-3" />
                <span>Copied!</span>
              </>
            ) : (
              <>
                <Copy className="w-3 h-3" />
                <span>Copy</span>
              </>
            )}
          </button>
          
          <button
            onClick={handleDownload}
            className="flex items-center space-x-1 px-2 py-1 text-xs bg-background border border-border rounded hover:bg-accent hover:text-accent-foreground transition-colors"
            title="Download file"
          >
            <Download className="w-3 h-3" />
            <span>Download</span>
          </button>
        </div>
      </div>

      {/* Code content */}
      <div className="relative">
        <pre className="overflow-x-auto p-4 text-sm bg-background">
          <code className="text-foreground font-mono">
            {showLineNumbers ? (
              <div className="table w-full">
                {lines.map((line, index) => (
                  <div key={index} className="table-row">
                    <span className="table-cell pr-4 text-muted-foreground select-none text-right w-8">
                      {index + 1}
                    </span>
                    <span className="table-cell">
                      {line || '\u00A0'}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              code
            )}
          </code>
        </pre>
      </div>
    </div>
  );
}
