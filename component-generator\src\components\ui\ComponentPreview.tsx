'use client';

import React from 'react';
import { ComponentConfig, NavbarConfig, ButtonConfig } from '@/types';
import { cn, getRoundedClass, getShadowClass } from '@/lib/utils';
import { ChevronRight, Loader2 } from 'lucide-react';

interface ComponentPreviewProps {
  config: ComponentConfig;
  className?: string;
}

// Preview components that render based on config
const NavbarPreview: React.FC<{ config: NavbarConfig }> = ({ config }) => {
  const { colors, size, rounded, shadow, position, transparent, logo, navigation, cta } = config;
  
  const baseClasses = [
    'w-full',
    position === 'fixed' ? 'fixed top-0 z-50' : position === 'sticky' ? 'sticky top-0 z-40' : '',
    transparent ? 'bg-transparent' : 'bg-white',
    !transparent ? getShadowClass(shadow) : '',
    getRoundedClass(rounded),
  ].filter(Boolean).join(' ');

  const containerClasses = [
    'container mx-auto px-4',
    size === 'sm' ? 'py-2' : size === 'lg' ? 'py-6' : 'py-4',
  ].join(' ');

  const logoClasses = [
    'font-bold',
    size === 'sm' ? 'text-lg' : size === 'lg' ? 'text-2xl' : 'text-xl',
  ].join(' ');

  const navItemClasses = [
    'hover:opacity-75 transition-opacity',
    size === 'sm' ? 'text-sm' : size === 'lg' ? 'text-lg' : 'text-base',
  ].join(' ');

  const ctaClasses = [
    'px-4 py-2 rounded-md font-medium transition-colors',
    size === 'sm' ? 'px-3 py-1 text-sm' : size === 'lg' ? 'px-6 py-3 text-lg' : '',
  ].filter(Boolean).join(' ');

  return (
    <nav 
      className={baseClasses}
      style={{ 
        backgroundColor: transparent ? 'transparent' : colors.background,
        color: colors.text 
      }}
    >
      <div className={containerClasses}>
        <div className="flex items-center justify-between">
          <div 
            className={logoClasses}
            style={{ color: colors.text }}
          >
            {logo.text}
          </div>
          
          <div className="hidden md:flex items-center space-x-8">
            {navigation.items.map((item, index) => (
              <a
                key={index}
                href={item.href}
                className={navItemClasses}
                style={{ 
                  color: item.active ? colors.primary : colors.text,
                  fontWeight: item.active ? '500' : '400'
                }}
              >
                {item.label}
              </a>
            ))}
          </div>
          
          {cta.enabled && (
            <button 
              className={ctaClasses}
              style={{
                backgroundColor: cta.variant === 'primary' ? colors.primary : 
                                cta.variant === 'secondary' ? colors.secondary : 'transparent',
                color: cta.variant === 'outline' ? colors.primary : colors.text,
                border: cta.variant === 'outline' ? `1px solid ${colors.primary}` : 'none'
              }}
            >
              {cta.text}
            </button>
          )}
        </div>
      </div>
    </nav>
  );
};

const ButtonPreview: React.FC<{ config: ButtonConfig }> = ({ config }) => {
  const { colors, size, rounded, shadow, variant, text, icon, fullWidth, disabled, loading } = config;
  
  const baseClasses = [
    'inline-flex items-center justify-center font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',
    fullWidth ? 'w-full' : '',
    getRoundedClass(rounded),
    getShadowClass(shadow),
  ].filter(Boolean);

  const sizeClasses = {
    sm: 'h-8 px-3 text-sm',
    md: 'h-10 px-4 py-2',
    lg: 'h-12 px-6 text-lg',
    xl: 'h-14 px-8 text-xl',
  };

  const buttonClasses = [
    ...baseClasses,
    sizeClasses[size],
  ].join(' ');

  const getButtonStyle = () => {
    switch (variant) {
      case 'primary':
        return {
          backgroundColor: colors.primary,
          color: colors.text,
        };
      case 'secondary':
        return {
          backgroundColor: colors.secondary,
          color: colors.text,
        };
      case 'outline':
        return {
          backgroundColor: 'transparent',
          color: colors.primary,
          border: `1px solid ${colors.primary}`,
        };
      case 'ghost':
        return {
          backgroundColor: 'transparent',
          color: colors.text,
        };
      case 'destructive':
        return {
          backgroundColor: '#ef4444',
          color: '#ffffff',
        };
      default:
        return {
          backgroundColor: colors.primary,
          color: colors.text,
        };
    }
  };

  return (
    <button
      className={buttonClasses}
      disabled={disabled || loading}
      style={getButtonStyle()}
    >
      {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
      {icon.enabled && icon.position === 'left' && !loading && (
        <ChevronRight className="mr-2 h-4 w-4" />
      )}
      {text}
      {icon.enabled && icon.position === 'right' && !loading && (
        <ChevronRight className="ml-2 h-4 w-4" />
      )}
    </button>
  );
};

export default function ComponentPreview({ config, className }: ComponentPreviewProps) {
  const renderPreview = () => {
    switch (config.type) {
      case 'navbar':
        return <NavbarPreview config={config as NavbarConfig} />;
      case 'button':
        return (
          <div className="flex items-center justify-center p-8">
            <ButtonPreview config={config as ButtonConfig} />
          </div>
        );
      case 'card':
        return (
          <div className="p-8 text-center text-muted-foreground border-2 border-dashed border-border rounded-lg">
            <p>Card preview coming soon...</p>
          </div>
        );
      case 'form':
        return (
          <div className="p-8 text-center text-muted-foreground border-2 border-dashed border-border rounded-lg">
            <p>Form preview coming soon...</p>
          </div>
        );
      default:
        return (
          <div className="p-8 text-center text-muted-foreground border-2 border-dashed border-border rounded-lg">
            <p>Select a component type to see preview</p>
          </div>
        );
    }
  };

  return (
    <div className={cn('bg-background border border-border rounded-lg overflow-hidden', className)}>
      {renderPreview()}
    </div>
  );
}
