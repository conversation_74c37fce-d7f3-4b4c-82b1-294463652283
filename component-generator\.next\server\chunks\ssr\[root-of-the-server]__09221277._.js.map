{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///I:/WebDev/component%20generator/component-generator/src/store/componentStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { devtools, persist } from 'zustand/middleware';\nimport { ComponentGeneratorState, ComponentConfig, ComponentType, NavbarConfig, ButtonConfig } from '@/types';\n\n// Default configurations for each component type\nconst defaultNavbarConfig: NavbarConfig = {\n  type: 'navbar',\n  size: 'md',\n  colors: {\n    primary: '#3b82f6',\n    secondary: '#64748b',\n    background: '#ffffff',\n    text: '#1f2937',\n    accent: '#f59e0b',\n  },\n  rounded: 'none',\n  shadow: 'sm',\n  font: 'Inter',\n  layout: 'horizontal',\n  position: 'static',\n  transparent: false,\n  logo: {\n    text: 'Logo',\n    position: 'left',\n  },\n  navigation: {\n    items: [\n      { label: 'Home', href: '/', active: true },\n      { label: 'About', href: '/about' },\n      { label: 'Services', href: '/services' },\n      { label: 'Contact', href: '/contact' },\n    ],\n    alignment: 'center',\n  },\n  cta: {\n    enabled: true,\n    text: 'Get Started',\n    variant: 'primary',\n  },\n};\n\nconst defaultButtonConfig: ButtonConfig = {\n  type: 'button',\n  size: 'md',\n  colors: {\n    primary: '#3b82f6',\n    secondary: '#64748b',\n    background: '#ffffff',\n    text: '#ffffff',\n    accent: '#f59e0b',\n  },\n  rounded: 'md',\n  shadow: 'sm',\n  font: 'Inter',\n  variant: 'primary',\n  text: 'Click me',\n  icon: {\n    enabled: false,\n    position: 'left',\n    name: 'arrow-right',\n  },\n  fullWidth: false,\n  disabled: false,\n  loading: false,\n};\n\nconst getDefaultConfig = (type: ComponentType): ComponentConfig => {\n  switch (type) {\n    case 'navbar':\n      return defaultNavbarConfig;\n    case 'button':\n      return defaultButtonConfig;\n    case 'card':\n      return {\n        type: 'card',\n        size: 'md',\n        colors: defaultNavbarConfig.colors,\n        rounded: 'lg',\n        shadow: 'md',\n        font: 'Inter',\n        layout: 'simple',\n        content: {\n          title: 'Card Title',\n          description: 'This is a sample card description that explains what this card is about.',\n          actions: [\n            { label: 'Learn More', variant: 'primary' },\n            { label: 'Cancel', variant: 'outline' },\n          ],\n        },\n        hover: {\n          enabled: true,\n          effect: 'lift',\n        },\n      };\n    case 'form':\n      return {\n        type: 'form',\n        size: 'md',\n        colors: defaultNavbarConfig.colors,\n        rounded: 'md',\n        shadow: 'sm',\n        font: 'Inter',\n        layout: 'vertical',\n        fields: [\n          { type: 'text', label: 'Name', placeholder: 'Enter your name', required: true },\n          { type: 'email', label: 'Email', placeholder: 'Enter your email', required: true },\n          { type: 'textarea', label: 'Message', placeholder: 'Enter your message', required: false },\n        ],\n        submitButton: {\n          text: 'Submit',\n          variant: 'primary',\n          fullWidth: true,\n        },\n      };\n    default:\n      return defaultNavbarConfig;\n  }\n};\n\nexport const useComponentStore = create<ComponentGeneratorState>()(\n  devtools(\n    persist(\n      (set, get) => ({\n        currentComponent: 'navbar',\n        config: defaultNavbarConfig,\n        generatedCode: '',\n        previewMode: 'desktop',\n        darkMode: false,\n\n        setCurrentComponent: (type: ComponentType) => {\n          const newConfig = getDefaultConfig(type);\n          set({\n            currentComponent: type,\n            config: newConfig,\n            generatedCode: '',\n          });\n        },\n\n        updateConfig: (newConfig: Partial<ComponentConfig>) => {\n          const currentConfig = get().config;\n          set({\n            config: { ...currentConfig, ...newConfig } as ComponentConfig,\n          });\n        },\n\n        setGeneratedCode: (code: string) => {\n          set({ generatedCode: code });\n        },\n\n        setPreviewMode: (mode: 'desktop' | 'tablet' | 'mobile') => {\n          set({ previewMode: mode });\n        },\n\n        toggleDarkMode: () => {\n          set((state) => ({ darkMode: !state.darkMode }));\n        },\n\n        resetConfig: () => {\n          const { currentComponent } = get();\n          const defaultConfig = getDefaultConfig(currentComponent);\n          set({\n            config: defaultConfig,\n            generatedCode: '',\n          });\n        },\n      }),\n      {\n        name: 'component-generator-storage',\n        partialize: (state) => ({\n          currentComponent: state.currentComponent,\n          config: state.config,\n          darkMode: state.darkMode,\n        }),\n      }\n    ),\n    {\n      name: 'component-generator',\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGA,iDAAiD;AACjD,MAAM,sBAAoC;IACxC,MAAM;IACN,MAAM;IACN,QAAQ;QACN,SAAS;QACT,WAAW;QACX,YAAY;QACZ,MAAM;QACN,QAAQ;IACV;IACA,SAAS;IACT,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,UAAU;IACV,aAAa;IACb,MAAM;QACJ,MAAM;QACN,UAAU;IACZ;IACA,YAAY;QACV,OAAO;YACL;gBAAE,OAAO;gBAAQ,MAAM;gBAAK,QAAQ;YAAK;YACzC;gBAAE,OAAO;gBAAS,MAAM;YAAS;YACjC;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAW,MAAM;YAAW;SACtC;QACD,WAAW;IACb;IACA,KAAK;QACH,SAAS;QACT,MAAM;QACN,SAAS;IACX;AACF;AAEA,MAAM,sBAAoC;IACxC,MAAM;IACN,MAAM;IACN,QAAQ;QACN,SAAS;QACT,WAAW;QACX,YAAY;QACZ,MAAM;QACN,QAAQ;IACV;IACA,SAAS;IACT,QAAQ;IACR,MAAM;IACN,SAAS;IACT,MAAM;IACN,MAAM;QACJ,SAAS;QACT,UAAU;QACV,MAAM;IACR;IACA,WAAW;IACX,UAAU;IACV,SAAS;AACX;AAEA,MAAM,mBAAmB,CAAC;IACxB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;gBACL,MAAM;gBACN,MAAM;gBACN,QAAQ,oBAAoB,MAAM;gBAClC,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,QAAQ;gBACR,SAAS;oBACP,OAAO;oBACP,aAAa;oBACb,SAAS;wBACP;4BAAE,OAAO;4BAAc,SAAS;wBAAU;wBAC1C;4BAAE,OAAO;4BAAU,SAAS;wBAAU;qBACvC;gBACH;gBACA,OAAO;oBACL,SAAS;oBACT,QAAQ;gBACV;YACF;QACF,KAAK;YACH,OAAO;gBACL,MAAM;gBACN,MAAM;gBACN,QAAQ,oBAAoB,MAAM;gBAClC,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,QAAQ;gBACR,QAAQ;oBACN;wBAAE,MAAM;wBAAQ,OAAO;wBAAQ,aAAa;wBAAmB,UAAU;oBAAK;oBAC9E;wBAAE,MAAM;wBAAS,OAAO;wBAAS,aAAa;wBAAoB,UAAU;oBAAK;oBACjF;wBAAE,MAAM;wBAAY,OAAO;wBAAW,aAAa;wBAAsB,UAAU;oBAAM;iBAC1F;gBACD,cAAc;oBACZ,MAAM;oBACN,SAAS;oBACT,WAAW;gBACb;YACF;QACF;YACE,OAAO;IACX;AACF;AAEO,MAAM,oBAAoB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACpC,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,kBAAkB;QAClB,QAAQ;QACR,eAAe;QACf,aAAa;QACb,UAAU;QAEV,qBAAqB,CAAC;YACpB,MAAM,YAAY,iBAAiB;YACnC,IAAI;gBACF,kBAAkB;gBAClB,QAAQ;gBACR,eAAe;YACjB;QACF;QAEA,cAAc,CAAC;YACb,MAAM,gBAAgB,MAAM,MAAM;YAClC,IAAI;gBACF,QAAQ;oBAAE,GAAG,aAAa;oBAAE,GAAG,SAAS;gBAAC;YAC3C;QACF;QAEA,kBAAkB,CAAC;YACjB,IAAI;gBAAE,eAAe;YAAK;QAC5B;QAEA,gBAAgB,CAAC;YACf,IAAI;gBAAE,aAAa;YAAK;QAC1B;QAEA,gBAAgB;YACd,IAAI,CAAC,QAAU,CAAC;oBAAE,UAAU,CAAC,MAAM,QAAQ;gBAAC,CAAC;QAC/C;QAEA,aAAa;YACX,MAAM,EAAE,gBAAgB,EAAE,GAAG;YAC7B,MAAM,gBAAgB,iBAAiB;YACvC,IAAI;gBACF,QAAQ;gBACR,eAAe;YACjB;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,kBAAkB,MAAM,gBAAgB;YACxC,QAAQ,MAAM,MAAM;YACpB,UAAU,MAAM,QAAQ;QAC1B,CAAC;AACH,IAEF;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///I:/WebDev/component%20generator/component-generator/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Convert hex color to CSS custom property\nexport function hexToHsl(hex: string): string {\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\n  if (!result) return '0 0% 0%';\n\n  const r = parseInt(result[1], 16) / 255;\n  const g = parseInt(result[2], 16) / 255;\n  const b = parseInt(result[3], 16) / 255;\n\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  let h = 0;\n  let s = 0;\n  const l = (max + min) / 2;\n\n  if (max === min) {\n    h = s = 0; // achromatic\n  } else {\n    const d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n\n  return `${Math.round(h * 360)} ${Math.round(s * 100)}% ${Math.round(l * 100)}%`;\n}\n\n// Generate CSS custom properties from color config\nexport function generateCSSVariables(colors: Record<string, string>): string {\n  return Object.entries(colors)\n    .map(([key, value]) => `  --${key}: ${hexToHsl(value)};`)\n    .join('\\n');\n}\n\n// Format generated code with Prettier (simplified version)\nexport function formatCode(code: string): string {\n  // Basic formatting - in a real app, you'd use Prettier API\n  return code\n    .replace(/\\s+/g, ' ')\n    .replace(/>\\s+</g, '><')\n    .replace(/{\\s+/g, '{ ')\n    .replace(/\\s+}/g, ' }')\n    .trim();\n}\n\n// Copy text to clipboard\nexport async function copyToClipboard(text: string): Promise<boolean> {\n  try {\n    await navigator.clipboard.writeText(text);\n    return true;\n  } catch (err) {\n    // Fallback for older browsers\n    const textArea = document.createElement('textarea');\n    textArea.value = text;\n    document.body.appendChild(textArea);\n    textArea.focus();\n    textArea.select();\n    try {\n      document.execCommand('copy');\n      document.body.removeChild(textArea);\n      return true;\n    } catch (fallbackErr) {\n      document.body.removeChild(textArea);\n      return false;\n    }\n  }\n}\n\n// Download text as file\nexport function downloadFile(content: string, filename: string, contentType = 'text/plain'): void {\n  const blob = new Blob([content], { type: contentType });\n  const url = URL.createObjectURL(blob);\n  const link = document.createElement('a');\n  link.href = url;\n  link.download = filename;\n  document.body.appendChild(link);\n  link.click();\n  document.body.removeChild(link);\n  URL.revokeObjectURL(url);\n}\n\n// Generate responsive classes based on size\nexport function getResponsiveClasses(size: string, property: string): string {\n  const sizeMap = {\n    sm: { base: '2', md: '3', lg: '4' },\n    md: { base: '4', md: '6', lg: '8' },\n    lg: { base: '6', md: '8', lg: '12' },\n    xl: { base: '8', md: '12', lg: '16' },\n  };\n\n  const sizes = sizeMap[size as keyof typeof sizeMap] || sizeMap.md;\n  return `${property}-${sizes.base} md:${property}-${sizes.md} lg:${property}-${sizes.lg}`;\n}\n\n// Generate shadow classes\nexport function getShadowClass(shadow: string): string {\n  const shadowMap = {\n    none: '',\n    sm: 'shadow-sm',\n    md: 'shadow-md',\n    lg: 'shadow-lg',\n    xl: 'shadow-xl',\n  };\n  return shadowMap[shadow as keyof typeof shadowMap] || '';\n}\n\n// Generate rounded classes\nexport function getRoundedClass(rounded: string): string {\n  const roundedMap = {\n    none: '',\n    sm: 'rounded-sm',\n    md: 'rounded-md',\n    lg: 'rounded-lg',\n    xl: 'rounded-xl',\n    full: 'rounded-full',\n  };\n  return roundedMap[rounded as keyof typeof roundedMap] || '';\n}\n\n// Debounce function for performance\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,SAAS,GAAW;IAClC,MAAM,SAAS,4CAA4C,IAAI,CAAC;IAChE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,IAAI,SAAS,MAAM,CAAC,EAAE,EAAE,MAAM;IACpC,MAAM,IAAI,SAAS,MAAM,CAAC,EAAE,EAAE,MAAM;IACpC,MAAM,IAAI,SAAS,MAAM,CAAC,EAAE,EAAE,MAAM;IAEpC,MAAM,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG;IAC3B,MAAM,MAAM,KAAK,GAAG,CAAC,GAAG,GAAG;IAC3B,IAAI,IAAI;IACR,IAAI,IAAI;IACR,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI;IAExB,IAAI,QAAQ,KAAK;QACf,IAAI,IAAI,GAAG,aAAa;IAC1B,OAAO;QACL,MAAM,IAAI,MAAM;QAChB,IAAI,IAAI,MAAM,IAAI,CAAC,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG;QAClD,OAAQ;YACN,KAAK;gBACH,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC;gBAChC;YACF,KAAK;gBACH,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;gBAClB;YACF,KAAK;gBACH,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;gBAClB;QACJ;QACA,KAAK;IACP;IAEA,OAAO,GAAG,KAAK,KAAK,CAAC,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE,EAAE,KAAK,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC;AACjF;AAGO,SAAS,qBAAqB,MAA8B;IACjE,OAAO,OAAO,OAAO,CAAC,QACnB,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,SAAS,OAAO,CAAC,CAAC,EACvD,IAAI,CAAC;AACV;AAGO,SAAS,WAAW,IAAY;IACrC,2DAA2D;IAC3D,OAAO,KACJ,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,UAAU,MAClB,OAAO,CAAC,SAAS,MACjB,OAAO,CAAC,SAAS,MACjB,IAAI;AACT;AAGO,eAAe,gBAAgB,IAAY;IAChD,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;QACpC,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,8BAA8B;QAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;QACxC,SAAS,KAAK,GAAG;QACjB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,SAAS,KAAK;QACd,SAAS,MAAM;QACf,IAAI;YACF,SAAS,WAAW,CAAC;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;QACT,EAAE,OAAO,aAAa;YACpB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO;QACT;IACF;AACF;AAGO,SAAS,aAAa,OAAe,EAAE,QAAgB,EAAE,cAAc,YAAY;IACxF,MAAM,OAAO,IAAI,KAAK;QAAC;KAAQ,EAAE;QAAE,MAAM;IAAY;IACrD,MAAM,MAAM,IAAI,eAAe,CAAC;IAChC,MAAM,OAAO,SAAS,aAAa,CAAC;IACpC,KAAK,IAAI,GAAG;IACZ,KAAK,QAAQ,GAAG;IAChB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,KAAK,KAAK;IACV,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,IAAI,eAAe,CAAC;AACtB;AAGO,SAAS,qBAAqB,IAAY,EAAE,QAAgB;IACjE,MAAM,UAAU;QACd,IAAI;YAAE,MAAM;YAAK,IAAI;YAAK,IAAI;QAAI;QAClC,IAAI;YAAE,MAAM;YAAK,IAAI;YAAK,IAAI;QAAI;QAClC,IAAI;YAAE,MAAM;YAAK,IAAI;YAAK,IAAI;QAAK;QACnC,IAAI;YAAE,MAAM;YAAK,IAAI;YAAM,IAAI;QAAK;IACtC;IAEA,MAAM,QAAQ,OAAO,CAAC,KAA6B,IAAI,QAAQ,EAAE;IACjE,OAAO,GAAG,SAAS,CAAC,EAAE,MAAM,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE,EAAE;AAC1F;AAGO,SAAS,eAAe,MAAc;IAC3C,MAAM,YAAY;QAChB,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IACA,OAAO,SAAS,CAAC,OAAiC,IAAI;AACxD;AAGO,SAAS,gBAAgB,OAAe;IAC7C,MAAM,aAAa;QACjB,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IACA,OAAO,UAAU,CAAC,QAAmC,IAAI;AAC3D;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///I:/WebDev/component%20generator/component-generator/src/lib/codeGenerator.ts"], "sourcesContent": ["import { ComponentConfig, NavbarConfig, ButtonConfig, CardConfig, FormConfig } from '@/types';\nimport { getRoundedClass, getShadowClass, generateCSSVariables } from './utils';\n\n// Base template for React components\nconst getComponentTemplate = (\n  componentName: string,\n  imports: string[],\n  props: string,\n  jsx: string,\n  styles?: string\n): string => {\n  const importsSection = imports.length > 0 ? imports.join('\\n') + '\\n\\n' : '';\n  const stylesSection = styles ? `\\nconst styles = \\`\\n${styles}\\`;\\n\\n` : '';\n  \n  return `${importsSection}${stylesSection}interface ${componentName}Props {\n${props}\n}\n\nexport default function ${componentName}({ ${props.split('\\n').map(line => {\n    const match = line.trim().match(/(\\w+):/);\n    return match ? match[1] : '';\n  }).filter(Boolean).join(', ')} }: ${componentName}Props) {\n  return (\n${jsx}\n  );\n}`;\n};\n\n// Generate Navbar component\nexport function generateNavbarCode(config: NavbarConfig): string {\n  const { colors, size, rounded, shadow, position, transparent, logo, navigation, cta } = config;\n  \n  const baseClasses = [\n    'w-full',\n    position === 'fixed' ? 'fixed top-0 z-50' : position === 'sticky' ? 'sticky top-0 z-40' : '',\n    transparent ? 'bg-transparent' : 'bg-background',\n    !transparent ? getShadowClass(shadow) : '',\n    getRoundedClass(rounded),\n  ].filter(Boolean).join(' ');\n\n  const containerClasses = [\n    'container mx-auto px-4',\n    size === 'sm' ? 'py-2' : size === 'lg' ? 'py-6' : 'py-4',\n  ].join(' ');\n\n  const logoClasses = [\n    'text-xl font-bold text-foreground',\n    size === 'sm' ? 'text-lg' : size === 'lg' ? 'text-2xl' : 'text-xl',\n  ].join(' ');\n\n  const navItemClasses = [\n    'text-muted-foreground hover:text-foreground transition-colors',\n    size === 'sm' ? 'text-sm' : size === 'lg' ? 'text-lg' : 'text-base',\n  ].join(' ');\n\n  const ctaClasses = [\n    'px-4 py-2 rounded-md font-medium transition-colors',\n    cta.variant === 'primary' ? 'bg-primary text-primary-foreground hover:bg-primary/90' :\n    cta.variant === 'secondary' ? 'bg-secondary text-secondary-foreground hover:bg-secondary/90' :\n    'border border-border text-foreground hover:bg-accent',\n    size === 'sm' ? 'px-3 py-1 text-sm' : size === 'lg' ? 'px-6 py-3 text-lg' : '',\n  ].filter(Boolean).join(' ');\n\n  const navItems = navigation.items.map(item => \n    `            <a\n              href=\"${item.href}\"\n              className=\"${navItemClasses}${item.active ? ' text-foreground font-medium' : ''}\"\n            >\n              ${item.label}\n            </a>`\n  ).join('\\n');\n\n  const jsx = `    <nav className=\"${baseClasses}\">\n      <div className=\"${containerClasses}\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"${logoClasses}\">\n            ${logo.text}\n          </div>\n          \n          <div className=\"hidden md:flex items-center space-x-8\">\n${navItems}\n          </div>\n          \n          ${cta.enabled ? `<button className=\"${ctaClasses}\">\n            ${cta.text}\n          </button>` : ''}\n        </div>\n      </div>\n    </nav>`;\n\n  const props = `  className?: string;`;\n  \n  const styles = generateCSSVariables(colors);\n\n  return getComponentTemplate('Navbar', [], props, jsx, styles);\n}\n\n// Generate Button component\nexport function generateButtonCode(config: ButtonConfig): string {\n  const { colors, size, rounded, shadow, variant, text, icon, fullWidth, disabled, loading } = config;\n  \n  const baseClasses = [\n    'inline-flex items-center justify-center font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',\n    fullWidth ? 'w-full' : '',\n    getRoundedClass(rounded),\n    getShadowClass(shadow),\n  ].filter(Boolean);\n\n  const sizeClasses = {\n    sm: 'h-8 px-3 text-sm',\n    md: 'h-10 px-4 py-2',\n    lg: 'h-12 px-6 text-lg',\n    xl: 'h-14 px-8 text-xl',\n  };\n\n  const variantClasses = {\n    primary: 'bg-primary text-primary-foreground hover:bg-primary/90',\n    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/90',\n    outline: 'border border-border bg-background hover:bg-accent hover:text-accent-foreground',\n    ghost: 'hover:bg-accent hover:text-accent-foreground',\n    destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n  };\n\n  const buttonClasses = [\n    ...baseClasses,\n    sizeClasses[size],\n    variantClasses[variant],\n  ].join(' ');\n\n  const iconElement = icon.enabled ? (\n    icon.position === 'left' \n      ? `        <ChevronRight className=\"mr-2 h-4 w-4\" />`\n      : `        <ChevronRight className=\"ml-2 h-4 w-4\" />`\n  ) : '';\n\n  const jsx = `    <button\n      className=\"${buttonClasses}\"\n      disabled={disabled || loading}\n      {...props}\n    >\n      ${loading ? `      <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />` : ''}\n      ${icon.enabled && icon.position === 'left' ? iconElement : ''}\n      {children || \"${text}\"}\n      ${icon.enabled && icon.position === 'right' ? iconElement : ''}\n    </button>`;\n\n  const imports = icon.enabled || loading ? [\n    `import { ChevronRight${loading ? ', Loader2' : ''} } from 'lucide-react';`\n  ] : [];\n\n  const props = `  children?: React.ReactNode;\n  disabled?: boolean;\n  loading?: boolean;\n  className?: string;\n  onClick?: () => void;`;\n\n  const styles = generateCSSVariables(colors);\n\n  return getComponentTemplate('Button', imports, props, jsx, styles);\n}\n\n// Main code generator function\nexport function generateComponentCode(config: ComponentConfig): string {\n  switch (config.type) {\n    case 'navbar':\n      return generateNavbarCode(config as NavbarConfig);\n    case 'button':\n      return generateButtonCode(config as ButtonConfig);\n    case 'card':\n      // TODO: Implement card generator\n      return '// Card generator coming soon...';\n    case 'form':\n      // TODO: Implement form generator\n      return '// Form generator coming soon...';\n    default:\n      return '// Component type not supported yet';\n  }\n}\n"], "names": [], "mappings": ";;;;;AACA;;AAEA,qCAAqC;AACrC,MAAM,uBAAuB,CAC3B,eACA,SACA,OACA,KACA;IAEA,MAAM,iBAAiB,QAAQ,MAAM,GAAG,IAAI,QAAQ,IAAI,CAAC,QAAQ,SAAS;IAC1E,MAAM,gBAAgB,SAAS,CAAC,qBAAqB,EAAE,OAAO,OAAO,CAAC,GAAG;IAEzE,OAAO,GAAG,iBAAiB,cAAc,UAAU,EAAE,cAAc;AACrE,EAAE,MAAM;;;wBAGgB,EAAE,cAAc,GAAG,EAAE,MAAM,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;QAC/D,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;QAChC,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;IAC5B,GAAG,MAAM,CAAC,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE,cAAc;;AAEpD,EAAE,IAAI;;CAEL,CAAC;AACF;AAGO,SAAS,mBAAmB,MAAoB;IACrD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG;IAExF,MAAM,cAAc;QAClB;QACA,aAAa,UAAU,qBAAqB,aAAa,WAAW,sBAAsB;QAC1F,cAAc,mBAAmB;QACjC,CAAC,cAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QACxC,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE;KACjB,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;IAEvB,MAAM,mBAAmB;QACvB;QACA,SAAS,OAAO,SAAS,SAAS,OAAO,SAAS;KACnD,CAAC,IAAI,CAAC;IAEP,MAAM,cAAc;QAClB;QACA,SAAS,OAAO,YAAY,SAAS,OAAO,aAAa;KAC1D,CAAC,IAAI,CAAC;IAEP,MAAM,iBAAiB;QACrB;QACA,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY;KACzD,CAAC,IAAI,CAAC;IAEP,MAAM,aAAa;QACjB;QACA,IAAI,OAAO,KAAK,YAAY,2DAC5B,IAAI,OAAO,KAAK,cAAc,iEAC9B;QACA,SAAS,OAAO,sBAAsB,SAAS,OAAO,sBAAsB;KAC7E,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;IAEvB,MAAM,WAAW,WAAW,KAAK,CAAC,GAAG,CAAC,CAAA,OACpC,CAAC;oBACe,EAAE,KAAK,IAAI,CAAC;yBACP,EAAE,iBAAiB,KAAK,MAAM,GAAG,iCAAiC,GAAG;;cAEhF,EAAE,KAAK,KAAK,CAAC;gBACX,CAAC,EACb,IAAI,CAAC;IAEP,MAAM,MAAM,CAAC,oBAAoB,EAAE,YAAY;sBAC3B,EAAE,iBAAiB;;0BAEf,EAAE,YAAY;YAC5B,EAAE,KAAK,IAAI,CAAC;;;;AAIxB,EAAE,SAAS;;;UAGD,EAAE,IAAI,OAAO,GAAG,CAAC,mBAAmB,EAAE,WAAW;YAC/C,EAAE,IAAI,IAAI,CAAC;mBACJ,CAAC,GAAG,GAAG;;;UAGhB,CAAC;IAET,MAAM,QAAQ,CAAC,qBAAqB,CAAC;IAErC,MAAM,SAAS,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD,EAAE;IAEpC,OAAO,qBAAqB,UAAU,EAAE,EAAE,OAAO,KAAK;AACxD;AAGO,SAAS,mBAAmB,MAAoB;IACrD,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;IAE7F,MAAM,cAAc;QAClB;QACA,YAAY,WAAW;QACvB,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE;QAChB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;KAChB,CAAC,MAAM,CAAC;IAET,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,gBAAgB;WACjB;QACH,WAAW,CAAC,KAAK;QACjB,cAAc,CAAC,QAAQ;KACxB,CAAC,IAAI,CAAC;IAEP,MAAM,cAAc,KAAK,OAAO,GAC9B,KAAK,QAAQ,KAAK,SACd,CAAC,iDAAiD,CAAC,GACnD,CAAC,iDAAiD,CAAC,GACrD;IAEJ,MAAM,MAAM,CAAC;iBACE,EAAE,cAAc;;;;MAI3B,EAAE,UAAU,CAAC,uDAAuD,CAAC,GAAG,GAAG;MAC3E,EAAE,KAAK,OAAO,IAAI,KAAK,QAAQ,KAAK,SAAS,cAAc,GAAG;oBAChD,EAAE,KAAK;MACrB,EAAE,KAAK,OAAO,IAAI,KAAK,QAAQ,KAAK,UAAU,cAAc,GAAG;aACxD,CAAC;IAEZ,MAAM,UAAU,KAAK,OAAO,IAAI,UAAU;QACxC,CAAC,qBAAqB,EAAE,UAAU,cAAc,GAAG,uBAAuB,CAAC;KAC5E,GAAG,EAAE;IAEN,MAAM,QAAQ,CAAC;;;;uBAIM,CAAC;IAEtB,MAAM,SAAS,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD,EAAE;IAEpC,OAAO,qBAAqB,UAAU,SAAS,OAAO,KAAK;AAC7D;AAGO,SAAS,sBAAsB,MAAuB;IAC3D,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO,mBAAmB;QAC5B,KAAK;YACH,OAAO,mBAAmB;QAC5B,KAAK;YACH,iCAAiC;YACjC,OAAO;QACT,KAAK;YACH,iCAAiC;YACjC,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///I:/WebDev/component%20generator/component-generator/src/components/ui/ComponentTypeSelector.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Navigation, Square, CreditCard, FileText, Zap, Layout } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { ComponentType } from '@/types';\n\ninterface ComponentTypeSelectorProps {\n  value: ComponentType;\n  onChange: (type: ComponentType) => void;\n  className?: string;\n}\n\nconst componentTypes: {\n  value: ComponentType;\n  label: string;\n  description: string;\n  icon: React.ComponentType<{ className?: string }>;\n  color: string;\n}[] = [\n  {\n    value: 'navbar',\n    label: 'Navigation Bar',\n    description: 'Header navigation with logo and menu items',\n    icon: Navigation,\n    color: 'text-blue-500',\n  },\n  {\n    value: 'button',\n    label: 'Button',\n    description: 'Interactive button with various styles',\n    icon: Square,\n    color: 'text-green-500',\n  },\n  {\n    value: 'card',\n    label: 'Card',\n    description: 'Content container with optional image and actions',\n    icon: CreditCard,\n    color: 'text-purple-500',\n  },\n  {\n    value: 'form',\n    label: 'Form',\n    description: 'Input form with validation and styling',\n    icon: FileText,\n    color: 'text-orange-500',\n  },\n  {\n    value: 'hero',\n    label: 'Hero Section',\n    description: 'Landing page hero with call-to-action',\n    icon: Zap,\n    color: 'text-red-500',\n  },\n  {\n    value: 'footer',\n    label: 'Footer',\n    description: 'Page footer with links and information',\n    icon: Layout,\n    color: 'text-indigo-500',\n  },\n];\n\nexport default function ComponentTypeSelector({ value, onChange, className }: ComponentTypeSelectorProps) {\n  return (\n    <div className={cn('space-y-3', className)}>\n      <h3 className=\"text-lg font-semibold text-foreground\">Choose Component Type</h3>\n      \n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n        {componentTypes.map((type) => {\n          const Icon = type.icon;\n          const isSelected = value === type.value;\n          \n          return (\n            <button\n              key={type.value}\n              onClick={() => onChange(type.value)}\n              className={cn(\n                'flex flex-col items-start p-4 text-left border rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\n                isSelected\n                  ? 'border-primary bg-primary/10 shadow-md'\n                  : 'border-border bg-card hover:bg-accent hover:border-accent-foreground/20'\n              )}\n            >\n              <div className=\"flex items-center space-x-3 mb-2\">\n                <Icon className={cn('w-5 h-5', isSelected ? 'text-primary' : type.color)} />\n                <span className={cn(\n                  'font-medium',\n                  isSelected ? 'text-primary' : 'text-foreground'\n                )}>\n                  {type.label}\n                </span>\n              </div>\n              \n              <p className={cn(\n                'text-sm',\n                isSelected ? 'text-primary/80' : 'text-muted-foreground'\n              )}>\n                {type.description}\n              </p>\n              \n              {isSelected && (\n                <div className=\"mt-2 px-2 py-1 bg-primary/20 text-primary text-xs rounded-full\">\n                  Selected\n                </div>\n              )}\n            </button>\n          );\n        })}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;AAaA,MAAM,iBAMA;IACJ;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,8MAAA,CAAA,aAAU;QAChB,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,kNAAA,CAAA,aAAU;QAChB,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,8MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,qNAAA,CAAA,SAAM;QACZ,OAAO;IACT;CACD;AAEc,SAAS,sBAAsB,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAA8B;IACtG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC9B,8OAAC;gBAAG,WAAU;0BAAwC;;;;;;0BAEtD,8OAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC;oBACnB,MAAM,OAAO,KAAK,IAAI;oBACtB,MAAM,aAAa,UAAU,KAAK,KAAK;oBAEvC,qBACE,8OAAC;wBAEC,SAAS,IAAM,SAAS,KAAK,KAAK;wBAClC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6JACA,aACI,2CACA;;0CAGN,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,aAAa,iBAAiB,KAAK,KAAK;;;;;;kDACvE,8OAAC;wCAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,eACA,aAAa,iBAAiB;kDAE7B,KAAK,KAAK;;;;;;;;;;;;0CAIf,8OAAC;gCAAE,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,WACA,aAAa,oBAAoB;0CAEhC,KAAK,WAAW;;;;;;4BAGlB,4BACC,8OAAC;gCAAI,WAAU;0CAAiE;;;;;;;uBA3B7E,KAAK,KAAK;;;;;gBAiCrB;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 665, "column": 0}, "map": {"version": 3, "sources": ["file:///I:/WebDev/component%20generator/component-generator/src/components/ui/PreviewContainer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Monitor, Tablet, Smartphone } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ninterface PreviewContainerProps {\n  children: React.ReactNode;\n  mode: 'desktop' | 'tablet' | 'mobile';\n  onModeChange: (mode: 'desktop' | 'tablet' | 'mobile') => void;\n  className?: string;\n}\n\nconst previewModes = [\n  { value: 'desktop' as const, label: 'Desktop', icon: Monitor, width: 'w-full' },\n  { value: 'tablet' as const, label: 'Tablet', icon: Tablet, width: 'w-[768px]' },\n  { value: 'mobile' as const, label: 'Mobile', icon: Smartphone, width: 'w-[375px]' },\n];\n\nexport default function PreviewContainer({ children, mode, onModeChange, className }: PreviewContainerProps) {\n  return (\n    <div className={cn('space-y-4', className)}>\n      {/* Preview mode selector */}\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-lg font-semibold text-foreground\">Live Preview</h3>\n        \n        <div className=\"flex items-center space-x-1 bg-muted p-1 rounded-lg\">\n          {previewModes.map((previewMode) => {\n            const Icon = previewMode.icon;\n            const isActive = mode === previewMode.value;\n            \n            return (\n              <button\n                key={previewMode.value}\n                onClick={() => onModeChange(previewMode.value)}\n                className={cn(\n                  'flex items-center space-x-2 px-3 py-2 text-sm rounded-md transition-colors',\n                  isActive\n                    ? 'bg-background text-foreground shadow-sm'\n                    : 'text-muted-foreground hover:text-foreground hover:bg-background/50'\n                )}\n                title={`Preview in ${previewMode.label} mode`}\n              >\n                <Icon className=\"w-4 h-4\" />\n                <span className=\"hidden sm:inline\">{previewMode.label}</span>\n              </button>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Preview container */}\n      <div className=\"bg-muted/30 border border-border rounded-lg p-6 overflow-x-auto\">\n        <div className=\"flex justify-center\">\n          <div\n            className={cn(\n              'transition-all duration-300 bg-background border border-border rounded-lg shadow-sm overflow-hidden',\n              previewModes.find(m => m.value === mode)?.width\n            )}\n          >\n            <div className=\"min-h-[200px] p-4\">\n              {children}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Preview info */}\n      <div className=\"flex items-center justify-center text-sm text-muted-foreground\">\n        <span>\n          Viewing in {mode} mode\n          {mode === 'tablet' && ' (768px)'}\n          {mode === 'mobile' && ' (375px)'}\n        </span>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAJA;;;;AAaA,MAAM,eAAe;IACnB;QAAE,OAAO;QAAoB,OAAO;QAAW,MAAM,wMAAA,CAAA,UAAO;QAAE,OAAO;IAAS;IAC9E;QAAE,OAAO;QAAmB,OAAO;QAAU,MAAM,sMAAA,CAAA,SAAM;QAAE,OAAO;IAAY;IAC9E;QAAE,OAAO;QAAmB,OAAO;QAAU,MAAM,8MAAA,CAAA,aAAU;QAAE,OAAO;IAAY;CACnF;AAEc,SAAS,iBAAiB,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,SAAS,EAAyB;IACzG,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAEtD,8OAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC;4BACjB,MAAM,OAAO,YAAY,IAAI;4BAC7B,MAAM,WAAW,SAAS,YAAY,KAAK;4BAE3C,qBACE,8OAAC;gCAEC,SAAS,IAAM,aAAa,YAAY,KAAK;gCAC7C,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8EACA,WACI,4CACA;gCAEN,OAAO,CAAC,WAAW,EAAE,YAAY,KAAK,CAAC,KAAK,CAAC;;kDAE7C,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAAoB,YAAY,KAAK;;;;;;;+BAXhD,YAAY,KAAK;;;;;wBAc5B;;;;;;;;;;;;0BAKJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,OAAO;kCAG5C,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;0BAOT,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;;wBAAK;wBACQ;wBAAK;wBAChB,SAAS,YAAY;wBACrB,SAAS,YAAY;;;;;;;;;;;;;;;;;;AAKhC", "debugId": null}}, {"offset": {"line": 815, "column": 0}, "map": {"version": 3, "sources": ["file:///I:/WebDev/component%20generator/component-generator/src/components/ui/CodeBlock.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Copy, Download, Check } from 'lucide-react';\nimport { cn, copyToClipboard, downloadFile } from '@/lib/utils';\n\ninterface CodeBlockProps {\n  code: string;\n  language?: string;\n  filename?: string;\n  className?: string;\n  showLineNumbers?: boolean;\n}\n\nexport default function CodeBlock({ \n  code, \n  language = 'tsx', \n  filename = 'Component.tsx',\n  className,\n  showLineNumbers = true \n}: CodeBlockProps) {\n  const [copied, setCopied] = useState(false);\n\n  const handleCopy = async () => {\n    const success = await copyToClipboard(code);\n    if (success) {\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    }\n  };\n\n  const handleDownload = () => {\n    downloadFile(code, filename, 'text/plain');\n  };\n\n  const lines = code.split('\\n');\n\n  return (\n    <div className={cn('relative bg-card border border-border rounded-lg overflow-hidden', className)}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between px-4 py-2 bg-muted border-b border-border\">\n        <div className=\"flex items-center space-x-2\">\n          <span className=\"text-sm font-medium text-foreground\">{filename}</span>\n          <span className=\"text-xs text-muted-foreground uppercase\">{language}</span>\n        </div>\n        \n        <div className=\"flex items-center space-x-2\">\n          <button\n            onClick={handleCopy}\n            className=\"flex items-center space-x-1 px-2 py-1 text-xs bg-background border border-border rounded hover:bg-accent hover:text-accent-foreground transition-colors\"\n            title=\"Copy to clipboard\"\n          >\n            {copied ? (\n              <>\n                <Check className=\"w-3 h-3\" />\n                <span>Copied!</span>\n              </>\n            ) : (\n              <>\n                <Copy className=\"w-3 h-3\" />\n                <span>Copy</span>\n              </>\n            )}\n          </button>\n          \n          <button\n            onClick={handleDownload}\n            className=\"flex items-center space-x-1 px-2 py-1 text-xs bg-background border border-border rounded hover:bg-accent hover:text-accent-foreground transition-colors\"\n            title=\"Download file\"\n          >\n            <Download className=\"w-3 h-3\" />\n            <span>Download</span>\n          </button>\n        </div>\n      </div>\n\n      {/* Code content */}\n      <div className=\"relative\">\n        <pre className=\"overflow-x-auto p-4 text-sm bg-background\">\n          <code className=\"text-foreground font-mono\">\n            {showLineNumbers ? (\n              <div className=\"table w-full\">\n                {lines.map((line, index) => (\n                  <div key={index} className=\"table-row\">\n                    <span className=\"table-cell pr-4 text-muted-foreground select-none text-right w-8\">\n                      {index + 1}\n                    </span>\n                    <span className=\"table-cell\">\n                      {line || '\\u00A0'}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            ) : (\n              code\n            )}\n          </code>\n        </pre>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAce,SAAS,UAAU,EAChC,IAAI,EACJ,WAAW,KAAK,EAChB,WAAW,eAAe,EAC1B,SAAS,EACT,kBAAkB,IAAI,EACP;IACf,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAAa;QACjB,MAAM,UAAU,MAAM,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE;QACtC,IAAI,SAAS;YACX,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC;IACF;IAEA,MAAM,iBAAiB;QACrB,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,UAAU;IAC/B;IAEA,MAAM,QAAQ,KAAK,KAAK,CAAC;IAEzB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;;0BAErF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAuC;;;;;;0CACvD,8OAAC;gCAAK,WAAU;0CAA2C;;;;;;;;;;;;kCAG7D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAM;0CAEL,uBACC;;sDACE,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;iEAGR;;sDACE,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;;0CAKZ,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAM;;kDAEN,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAU;kCACb,gCACC,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAK,WAAU;sDACb,QAAQ;;;;;;sDAEX,8OAAC;4CAAK,WAAU;sDACb,QAAQ;;;;;;;mCALH;;;;;;;;;mCAWd;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 1021, "column": 0}, "map": {"version": 3, "sources": ["file:///I:/WebDev/component%20generator/component-generator/src/components/ui/ComponentPreview.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { ComponentConfig, NavbarConfig, ButtonConfig } from '@/types';\nimport { cn, getRoundedClass, getShadowClass } from '@/lib/utils';\nimport { ChevronRight, Loader2 } from 'lucide-react';\n\ninterface ComponentPreviewProps {\n  config: ComponentConfig;\n  className?: string;\n}\n\n// Preview components that render based on config\nconst NavbarPreview: React.FC<{ config: NavbarConfig }> = ({ config }) => {\n  const { colors, size, rounded, shadow, position, transparent, logo, navigation, cta } = config;\n  \n  const baseClasses = [\n    'w-full',\n    position === 'fixed' ? 'fixed top-0 z-50' : position === 'sticky' ? 'sticky top-0 z-40' : '',\n    transparent ? 'bg-transparent' : 'bg-white',\n    !transparent ? getShadowClass(shadow) : '',\n    getRoundedClass(rounded),\n  ].filter(Boolean).join(' ');\n\n  const containerClasses = [\n    'container mx-auto px-4',\n    size === 'sm' ? 'py-2' : size === 'lg' ? 'py-6' : 'py-4',\n  ].join(' ');\n\n  const logoClasses = [\n    'font-bold',\n    size === 'sm' ? 'text-lg' : size === 'lg' ? 'text-2xl' : 'text-xl',\n  ].join(' ');\n\n  const navItemClasses = [\n    'hover:opacity-75 transition-opacity',\n    size === 'sm' ? 'text-sm' : size === 'lg' ? 'text-lg' : 'text-base',\n  ].join(' ');\n\n  const ctaClasses = [\n    'px-4 py-2 rounded-md font-medium transition-colors',\n    size === 'sm' ? 'px-3 py-1 text-sm' : size === 'lg' ? 'px-6 py-3 text-lg' : '',\n  ].filter(Boolean).join(' ');\n\n  return (\n    <nav \n      className={baseClasses}\n      style={{ \n        backgroundColor: transparent ? 'transparent' : colors.background,\n        color: colors.text \n      }}\n    >\n      <div className={containerClasses}>\n        <div className=\"flex items-center justify-between\">\n          <div \n            className={logoClasses}\n            style={{ color: colors.text }}\n          >\n            {logo.text}\n          </div>\n          \n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navigation.items.map((item, index) => (\n              <a\n                key={index}\n                href={item.href}\n                className={navItemClasses}\n                style={{ \n                  color: item.active ? colors.primary : colors.text,\n                  fontWeight: item.active ? '500' : '400'\n                }}\n              >\n                {item.label}\n              </a>\n            ))}\n          </div>\n          \n          {cta.enabled && (\n            <button \n              className={ctaClasses}\n              style={{\n                backgroundColor: cta.variant === 'primary' ? colors.primary : \n                                cta.variant === 'secondary' ? colors.secondary : 'transparent',\n                color: cta.variant === 'outline' ? colors.primary : colors.text,\n                border: cta.variant === 'outline' ? `1px solid ${colors.primary}` : 'none'\n              }}\n            >\n              {cta.text}\n            </button>\n          )}\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nconst ButtonPreview: React.FC<{ config: ButtonConfig }> = ({ config }) => {\n  const { colors, size, rounded, shadow, variant, text, icon, fullWidth, disabled, loading } = config;\n  \n  const baseClasses = [\n    'inline-flex items-center justify-center font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',\n    fullWidth ? 'w-full' : '',\n    getRoundedClass(rounded),\n    getShadowClass(shadow),\n  ].filter(Boolean);\n\n  const sizeClasses = {\n    sm: 'h-8 px-3 text-sm',\n    md: 'h-10 px-4 py-2',\n    lg: 'h-12 px-6 text-lg',\n    xl: 'h-14 px-8 text-xl',\n  };\n\n  const buttonClasses = [\n    ...baseClasses,\n    sizeClasses[size],\n  ].join(' ');\n\n  const getButtonStyle = () => {\n    switch (variant) {\n      case 'primary':\n        return {\n          backgroundColor: colors.primary,\n          color: colors.text,\n        };\n      case 'secondary':\n        return {\n          backgroundColor: colors.secondary,\n          color: colors.text,\n        };\n      case 'outline':\n        return {\n          backgroundColor: 'transparent',\n          color: colors.primary,\n          border: `1px solid ${colors.primary}`,\n        };\n      case 'ghost':\n        return {\n          backgroundColor: 'transparent',\n          color: colors.text,\n        };\n      case 'destructive':\n        return {\n          backgroundColor: '#ef4444',\n          color: '#ffffff',\n        };\n      default:\n        return {\n          backgroundColor: colors.primary,\n          color: colors.text,\n        };\n    }\n  };\n\n  return (\n    <button\n      className={buttonClasses}\n      disabled={disabled || loading}\n      style={getButtonStyle()}\n    >\n      {loading && <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />}\n      {icon.enabled && icon.position === 'left' && !loading && (\n        <ChevronRight className=\"mr-2 h-4 w-4\" />\n      )}\n      {text}\n      {icon.enabled && icon.position === 'right' && !loading && (\n        <ChevronRight className=\"ml-2 h-4 w-4\" />\n      )}\n    </button>\n  );\n};\n\nexport default function ComponentPreview({ config, className }: ComponentPreviewProps) {\n  const renderPreview = () => {\n    switch (config.type) {\n      case 'navbar':\n        return <NavbarPreview config={config as NavbarConfig} />;\n      case 'button':\n        return (\n          <div className=\"flex items-center justify-center p-8\">\n            <ButtonPreview config={config as ButtonConfig} />\n          </div>\n        );\n      case 'card':\n        return (\n          <div className=\"p-8 text-center text-muted-foreground border-2 border-dashed border-border rounded-lg\">\n            <p>Card preview coming soon...</p>\n          </div>\n        );\n      case 'form':\n        return (\n          <div className=\"p-8 text-center text-muted-foreground border-2 border-dashed border-border rounded-lg\">\n            <p>Form preview coming soon...</p>\n          </div>\n        );\n      default:\n        return (\n          <div className=\"p-8 text-center text-muted-foreground border-2 border-dashed border-border rounded-lg\">\n            <p>Select a component type to see preview</p>\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div className={cn('bg-background border border-border rounded-lg overflow-hidden', className)}>\n      {renderPreview()}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AAAA;AALA;;;;AAYA,iDAAiD;AACjD,MAAM,gBAAoD,CAAC,EAAE,MAAM,EAAE;IACnE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG;IAExF,MAAM,cAAc;QAClB;QACA,aAAa,UAAU,qBAAqB,aAAa,WAAW,sBAAsB;QAC1F,cAAc,mBAAmB;QACjC,CAAC,cAAc,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QACxC,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE;KACjB,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;IAEvB,MAAM,mBAAmB;QACvB;QACA,SAAS,OAAO,SAAS,SAAS,OAAO,SAAS;KACnD,CAAC,IAAI,CAAC;IAEP,MAAM,cAAc;QAClB;QACA,SAAS,OAAO,YAAY,SAAS,OAAO,aAAa;KAC1D,CAAC,IAAI,CAAC;IAEP,MAAM,iBAAiB;QACrB;QACA,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY;KACzD,CAAC,IAAI,CAAC;IAEP,MAAM,aAAa;QACjB;QACA,SAAS,OAAO,sBAAsB,SAAS,OAAO,sBAAsB;KAC7E,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;IAEvB,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;YACL,iBAAiB,cAAc,gBAAgB,OAAO,UAAU;YAChE,OAAO,OAAO,IAAI;QACpB;kBAEA,cAAA,8OAAC;YAAI,WAAW;sBACd,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAW;wBACX,OAAO;4BAAE,OAAO,OAAO,IAAI;wBAAC;kCAE3B,KAAK,IAAI;;;;;;kCAGZ,8OAAC;wBAAI,WAAU;kCACZ,WAAW,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC3B,8OAAC;gCAEC,MAAM,KAAK,IAAI;gCACf,WAAW;gCACX,OAAO;oCACL,OAAO,KAAK,MAAM,GAAG,OAAO,OAAO,GAAG,OAAO,IAAI;oCACjD,YAAY,KAAK,MAAM,GAAG,QAAQ;gCACpC;0CAEC,KAAK,KAAK;+BARN;;;;;;;;;;oBAaV,IAAI,OAAO,kBACV,8OAAC;wBACC,WAAW;wBACX,OAAO;4BACL,iBAAiB,IAAI,OAAO,KAAK,YAAY,OAAO,OAAO,GAC3C,IAAI,OAAO,KAAK,cAAc,OAAO,SAAS,GAAG;4BACjE,OAAO,IAAI,OAAO,KAAK,YAAY,OAAO,OAAO,GAAG,OAAO,IAAI;4BAC/D,QAAQ,IAAI,OAAO,KAAK,YAAY,CAAC,UAAU,EAAE,OAAO,OAAO,EAAE,GAAG;wBACtE;kCAEC,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;;AAOvB;AAEA,MAAM,gBAAoD,CAAC,EAAE,MAAM,EAAE;IACnE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG;IAE7F,MAAM,cAAc;QAClB;QACA,YAAY,WAAW;QACvB,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD,EAAE;QAChB,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;KAChB,CAAC,MAAM,CAAC;IAET,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,gBAAgB;WACjB;QACH,WAAW,CAAC,KAAK;KAClB,CAAC,IAAI,CAAC;IAEP,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,iBAAiB,OAAO,OAAO;oBAC/B,OAAO,OAAO,IAAI;gBACpB;YACF,KAAK;gBACH,OAAO;oBACL,iBAAiB,OAAO,SAAS;oBACjC,OAAO,OAAO,IAAI;gBACpB;YACF,KAAK;gBACH,OAAO;oBACL,iBAAiB;oBACjB,OAAO,OAAO,OAAO;oBACrB,QAAQ,CAAC,UAAU,EAAE,OAAO,OAAO,EAAE;gBACvC;YACF,KAAK;gBACH,OAAO;oBACL,iBAAiB;oBACjB,OAAO,OAAO,IAAI;gBACpB;YACF,KAAK;gBACH,OAAO;oBACL,iBAAiB;oBACjB,OAAO;gBACT;YACF;gBACE,OAAO;oBACL,iBAAiB,OAAO,OAAO;oBAC/B,OAAO,OAAO,IAAI;gBACpB;QACJ;IACF;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,UAAU,YAAY;QACtB,OAAO;;YAEN,yBAAW,8OAAC,iNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YAC9B,KAAK,OAAO,IAAI,KAAK,QAAQ,KAAK,UAAU,CAAC,yBAC5C,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;YAEzB;YACA,KAAK,OAAO,IAAI,KAAK,QAAQ,KAAK,WAAW,CAAC,yBAC7C,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAIhC;AAEe,SAAS,iBAAiB,EAAE,MAAM,EAAE,SAAS,EAAyB;IACnF,MAAM,gBAAgB;QACpB,OAAQ,OAAO,IAAI;YACjB,KAAK;gBACH,qBAAO,8OAAC;oBAAc,QAAQ;;;;;;YAChC,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAc,QAAQ;;;;;;;;;;;YAG7B,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;YAGT,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;YAGT;gBACE,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;QAGX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iEAAiE;kBACjF;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": ["file:///I:/WebDev/component%20generator/component-generator/src/components/ui/ColorPicker.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { SketchPicker, ColorResult } from 'react-color';\nimport { cn } from '@/lib/utils';\n\ninterface ColorPickerProps {\n  color: string;\n  onChange: (color: string) => void;\n  label?: string;\n  className?: string;\n}\n\nexport default function ColorPicker({ color, onChange, label, className }: ColorPickerProps) {\n  const [isOpen, setIsOpen] = useState(false);\n\n  const handleColorChange = (colorResult: ColorResult) => {\n    onChange(colorResult.hex);\n  };\n\n  return (\n    <div className={cn('relative', className)}>\n      {label && (\n        <label className=\"block text-sm font-medium text-foreground mb-2\">\n          {label}\n        </label>\n      )}\n      \n      <div className=\"flex items-center space-x-3\">\n        <button\n          type=\"button\"\n          onClick={() => setIsOpen(!isOpen)}\n          className=\"w-10 h-10 rounded-md border-2 border-border shadow-sm focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\"\n          style={{ backgroundColor: color }}\n          aria-label={`Select color (current: ${color})`}\n        />\n        \n        <div className=\"flex-1\">\n          <input\n            type=\"text\"\n            value={color}\n            onChange={(e) => onChange(e.target.value)}\n            className=\"w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\"\n            placeholder=\"#000000\"\n          />\n        </div>\n      </div>\n\n      {isOpen && (\n        <>\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsOpen(false)}\n          />\n          <div className=\"absolute top-full left-0 z-20 mt-2\">\n            <SketchPicker\n              color={color}\n              onChange={handleColorChange}\n              disableAlpha\n            />\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAae,SAAS,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAoB;IACzF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,oBAAoB,CAAC;QACzB,SAAS,YAAY,GAAG;IAC1B;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;YAC5B,uBACC,8OAAC;gBAAM,WAAU;0BACd;;;;;;0BAIL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,UAAU,CAAC;wBAC1B,WAAU;wBACV,OAAO;4BAAE,iBAAiB;wBAAM;wBAChC,cAAY,CAAC,uBAAuB,EAAE,MAAM,CAAC,CAAC;;;;;;kCAGhD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,WAAU;4BACV,aAAY;;;;;;;;;;;;;;;;;YAKjB,wBACC;;kCACE,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAE3B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iNAAA,CAAA,eAAY;4BACX,OAAO;4BACP,UAAU;4BACV,YAAY;;;;;;;;;;;;;;;;;;;AAO1B", "debugId": null}}, {"offset": {"line": 1411, "column": 0}, "map": {"version": 3, "sources": ["file:///I:/WebDev/component%20generator/component-generator/src/components/ui/SizeSelector.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\nimport { SizeOption } from '@/types';\n\ninterface SizeSelectorProps {\n  value: SizeOption;\n  onChange: (size: SizeOption) => void;\n  label?: string;\n  className?: string;\n}\n\nconst sizeOptions: { value: SizeOption; label: string; description: string }[] = [\n  { value: 'sm', label: 'Small', description: 'Compact size' },\n  { value: 'md', label: 'Medium', description: 'Default size' },\n  { value: 'lg', label: 'Large', description: 'Spacious size' },\n  { value: 'xl', label: 'Extra Large', description: 'Maximum size' },\n];\n\nexport default function SizeSelector({ value, onChange, label, className }: SizeSelectorProps) {\n  return (\n    <div className={cn('space-y-2', className)}>\n      {label && (\n        <label className=\"block text-sm font-medium text-foreground\">\n          {label}\n        </label>\n      )}\n      \n      <div className=\"grid grid-cols-2 gap-2\">\n        {sizeOptions.map((option) => (\n          <button\n            key={option.value}\n            type=\"button\"\n            onClick={() => onChange(option.value)}\n            className={cn(\n              'flex flex-col items-center justify-center p-3 text-sm border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\n              value === option.value\n                ? 'border-primary bg-primary/10 text-primary'\n                : 'border-border bg-background text-muted-foreground hover:bg-accent hover:text-accent-foreground'\n            )}\n          >\n            <span className=\"font-medium\">{option.label}</span>\n            <span className=\"text-xs opacity-70\">{option.description}</span>\n          </button>\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAaA,MAAM,cAA2E;IAC/E;QAAE,OAAO;QAAM,OAAO;QAAS,aAAa;IAAe;IAC3D;QAAE,OAAO;QAAM,OAAO;QAAU,aAAa;IAAe;IAC5D;QAAE,OAAO;QAAM,OAAO;QAAS,aAAa;IAAgB;IAC5D;QAAE,OAAO;QAAM,OAAO;QAAe,aAAa;IAAe;CAClE;AAEc,SAAS,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAqB;IAC3F,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;YAC7B,uBACC,8OAAC;gBAAM,WAAU;0BACd;;;;;;0BAIL,8OAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;wBAEC,MAAK;wBACL,SAAS,IAAM,SAAS,OAAO,KAAK;wBACpC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iKACA,UAAU,OAAO,KAAK,GAClB,8CACA;;0CAGN,8OAAC;gCAAK,WAAU;0CAAe,OAAO,KAAK;;;;;;0CAC3C,8OAAC;gCAAK,WAAU;0CAAsB,OAAO,WAAW;;;;;;;uBAXnD,OAAO,KAAK;;;;;;;;;;;;;;;;AAiB7B", "debugId": null}}, {"offset": {"line": 1498, "column": 0}, "map": {"version": 3, "sources": ["file:///I:/WebDev/component%20generator/component-generator/src/components/generators/NavbarGenerator.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useComponentStore } from '@/store/componentStore';\nimport { NavbarConfig } from '@/types';\nimport ColorPicker from '@/components/ui/ColorPicker';\nimport SizeSelector from '@/components/ui/SizeSelector';\n\nexport default function NavbarGenerator() {\n  const { config, updateConfig } = useComponentStore();\n  const navbarConfig = config as NavbarConfig;\n\n  const updateNavbarConfig = (updates: Partial<NavbarConfig>) => {\n    updateConfig(updates);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Size Selection */}\n      <SizeSelector\n        value={navbarConfig.size}\n        onChange={(size) => updateNavbarConfig({ size })}\n        label=\"Size\"\n      />\n\n      {/* Colors */}\n      <div className=\"space-y-4\">\n        <h4 className=\"text-sm font-medium text-foreground\">Colors</h4>\n        <div className=\"grid grid-cols-1 gap-4\">\n          <ColorPicker\n            color={navbarConfig.colors.primary}\n            onChange={(color) => updateNavbarConfig({\n              colors: { ...navbarConfig.colors, primary: color }\n            })}\n            label=\"Primary Color\"\n          />\n          <ColorPicker\n            color={navbarConfig.colors.background}\n            onChange={(color) => updateNavbarConfig({\n              colors: { ...navbarConfig.colors, background: color }\n            })}\n            label=\"Background Color\"\n          />\n          <ColorPicker\n            color={navbarConfig.colors.text}\n            onChange={(color) => updateNavbarConfig({\n              colors: { ...navbarConfig.colors, text: color }\n            })}\n            label=\"Text Color\"\n          />\n        </div>\n      </div>\n\n      {/* Layout Options */}\n      <div className=\"space-y-3\">\n        <h4 className=\"text-sm font-medium text-foreground\">Layout</h4>\n        \n        <div className=\"space-y-2\">\n          <label className=\"block text-sm text-muted-foreground\">Position</label>\n          <select\n            value={navbarConfig.position}\n            onChange={(e) => updateNavbarConfig({ position: e.target.value as any })}\n            className=\"w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n          >\n            <option value=\"static\">Static</option>\n            <option value=\"fixed\">Fixed</option>\n            <option value=\"sticky\">Sticky</option>\n          </select>\n        </div>\n\n        <div className=\"space-y-2\">\n          <label className=\"block text-sm text-muted-foreground\">Navigation Alignment</label>\n          <select\n            value={navbarConfig.navigation.alignment}\n            onChange={(e) => updateNavbarConfig({\n              navigation: { ...navbarConfig.navigation, alignment: e.target.value as any }\n            })}\n            className=\"w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n          >\n            <option value=\"left\">Left</option>\n            <option value=\"center\">Center</option>\n            <option value=\"right\">Right</option>\n            <option value=\"space-between\">Space Between</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Logo Configuration */}\n      <div className=\"space-y-3\">\n        <h4 className=\"text-sm font-medium text-foreground\">Logo</h4>\n        \n        <div className=\"space-y-2\">\n          <label className=\"block text-sm text-muted-foreground\">Logo Text</label>\n          <input\n            type=\"text\"\n            value={navbarConfig.logo.text}\n            onChange={(e) => updateNavbarConfig({\n              logo: { ...navbarConfig.logo, text: e.target.value }\n            })}\n            className=\"w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n            placeholder=\"Enter logo text\"\n          />\n        </div>\n\n        <div className=\"space-y-2\">\n          <label className=\"block text-sm text-muted-foreground\">Logo Position</label>\n          <select\n            value={navbarConfig.logo.position}\n            onChange={(e) => updateNavbarConfig({\n              logo: { ...navbarConfig.logo, position: e.target.value as any }\n            })}\n            className=\"w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n          >\n            <option value=\"left\">Left</option>\n            <option value=\"center\">Center</option>\n            <option value=\"right\">Right</option>\n          </select>\n        </div>\n      </div>\n\n      {/* CTA Button */}\n      <div className=\"space-y-3\">\n        <h4 className=\"text-sm font-medium text-foreground\">Call to Action</h4>\n        \n        <div className=\"flex items-center space-x-2\">\n          <input\n            type=\"checkbox\"\n            id=\"cta-enabled\"\n            checked={navbarConfig.cta.enabled}\n            onChange={(e) => updateNavbarConfig({\n              cta: { ...navbarConfig.cta, enabled: e.target.checked }\n            })}\n            className=\"rounded border-border text-primary focus:ring-ring\"\n          />\n          <label htmlFor=\"cta-enabled\" className=\"text-sm text-muted-foreground\">\n            Enable CTA Button\n          </label>\n        </div>\n\n        {navbarConfig.cta.enabled && (\n          <div className=\"space-y-2\">\n            <div>\n              <label className=\"block text-sm text-muted-foreground\">Button Text</label>\n              <input\n                type=\"text\"\n                value={navbarConfig.cta.text}\n                onChange={(e) => updateNavbarConfig({\n                  cta: { ...navbarConfig.cta, text: e.target.value }\n                })}\n                className=\"w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n                placeholder=\"Enter button text\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm text-muted-foreground\">Button Variant</label>\n              <select\n                value={navbarConfig.cta.variant}\n                onChange={(e) => updateNavbarConfig({\n                  cta: { ...navbarConfig.cta, variant: e.target.value as any }\n                })}\n                className=\"w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n              >\n                <option value=\"primary\">Primary</option>\n                <option value=\"secondary\">Secondary</option>\n                <option value=\"outline\">Outline</option>\n              </select>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Style Options */}\n      <div className=\"space-y-3\">\n        <h4 className=\"text-sm font-medium text-foreground\">Style</h4>\n        \n        <div className=\"space-y-2\">\n          <label className=\"block text-sm text-muted-foreground\">Border Radius</label>\n          <select\n            value={navbarConfig.rounded}\n            onChange={(e) => updateNavbarConfig({ rounded: e.target.value as any })}\n            className=\"w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n          >\n            <option value=\"none\">None</option>\n            <option value=\"sm\">Small</option>\n            <option value=\"md\">Medium</option>\n            <option value=\"lg\">Large</option>\n            <option value=\"xl\">Extra Large</option>\n          </select>\n        </div>\n\n        <div className=\"space-y-2\">\n          <label className=\"block text-sm text-muted-foreground\">Shadow</label>\n          <select\n            value={navbarConfig.shadow}\n            onChange={(e) => updateNavbarConfig({ shadow: e.target.value as any })}\n            className=\"w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n          >\n            <option value=\"none\">None</option>\n            <option value=\"sm\">Small</option>\n            <option value=\"md\">Medium</option>\n            <option value=\"lg\">Large</option>\n            <option value=\"xl\">Extra Large</option>\n          </select>\n        </div>\n\n        <div className=\"flex items-center space-x-2\">\n          <input\n            type=\"checkbox\"\n            id=\"transparent\"\n            checked={navbarConfig.transparent}\n            onChange={(e) => updateNavbarConfig({ transparent: e.target.checked })}\n            className=\"rounded border-border text-primary focus:ring-ring\"\n          />\n          <label htmlFor=\"transparent\" className=\"text-sm text-muted-foreground\">\n            Transparent Background\n          </label>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AANA;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD;IACjD,MAAM,eAAe;IAErB,MAAM,qBAAqB,CAAC;QAC1B,aAAa;IACf;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,wIAAA,CAAA,UAAY;gBACX,OAAO,aAAa,IAAI;gBACxB,UAAU,CAAC,OAAS,mBAAmB;wBAAE;oBAAK;gBAC9C,OAAM;;;;;;0BAIR,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,uIAAA,CAAA,UAAW;gCACV,OAAO,aAAa,MAAM,CAAC,OAAO;gCAClC,UAAU,CAAC,QAAU,mBAAmB;wCACtC,QAAQ;4CAAE,GAAG,aAAa,MAAM;4CAAE,SAAS;wCAAM;oCACnD;gCACA,OAAM;;;;;;0CAER,8OAAC,uIAAA,CAAA,UAAW;gCACV,OAAO,aAAa,MAAM,CAAC,UAAU;gCACrC,UAAU,CAAC,QAAU,mBAAmB;wCACtC,QAAQ;4CAAE,GAAG,aAAa,MAAM;4CAAE,YAAY;wCAAM;oCACtD;gCACA,OAAM;;;;;;0CAER,8OAAC,uIAAA,CAAA,UAAW;gCACV,OAAO,aAAa,MAAM,CAAC,IAAI;gCAC/B,UAAU,CAAC,QAAU,mBAAmB;wCACtC,QAAQ;4CAAE,GAAG,aAAa,MAAM;4CAAE,MAAM;wCAAM;oCAChD;gCACA,OAAM;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAsC;;;;;;0CACvD,8OAAC;gCACC,OAAO,aAAa,QAAQ;gCAC5B,UAAU,CAAC,IAAM,mBAAmB;wCAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oCAAQ;gCACtE,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;;;;;;;;;;;;;kCAI3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAsC;;;;;;0CACvD,8OAAC;gCACC,OAAO,aAAa,UAAU,CAAC,SAAS;gCACxC,UAAU,CAAC,IAAM,mBAAmB;wCAClC,YAAY;4CAAE,GAAG,aAAa,UAAU;4CAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wCAAQ;oCAC7E;gCACA,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;kDACtB,8OAAC;wCAAO,OAAM;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;0BAMpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAsC;;;;;;0CACvD,8OAAC;gCACC,MAAK;gCACL,OAAO,aAAa,IAAI,CAAC,IAAI;gCAC7B,UAAU,CAAC,IAAM,mBAAmB;wCAClC,MAAM;4CAAE,GAAG,aAAa,IAAI;4CAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wCAAC;oCACrD;gCACA,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAIhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAsC;;;;;;0CACvD,8OAAC;gCACC,OAAO,aAAa,IAAI,CAAC,QAAQ;gCACjC,UAAU,CAAC,IAAM,mBAAmB;wCAClC,MAAM;4CAAE,GAAG,aAAa,IAAI;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wCAAQ;oCAChE;gCACA,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAS;;;;;;kDACvB,8OAAC;wCAAO,OAAM;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;0BAM5B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,IAAG;gCACH,SAAS,aAAa,GAAG,CAAC,OAAO;gCACjC,UAAU,CAAC,IAAM,mBAAmB;wCAClC,KAAK;4CAAE,GAAG,aAAa,GAAG;4CAAE,SAAS,EAAE,MAAM,CAAC,OAAO;wCAAC;oCACxD;gCACA,WAAU;;;;;;0CAEZ,8OAAC;gCAAM,SAAQ;gCAAc,WAAU;0CAAgC;;;;;;;;;;;;oBAKxE,aAAa,GAAG,CAAC,OAAO,kBACvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAsC;;;;;;kDACvD,8OAAC;wCACC,MAAK;wCACL,OAAO,aAAa,GAAG,CAAC,IAAI;wCAC5B,UAAU,CAAC,IAAM,mBAAmB;gDAClC,KAAK;oDAAE,GAAG,aAAa,GAAG;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACnD;wCACA,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAsC;;;;;;kDACvD,8OAAC;wCACC,OAAO,aAAa,GAAG,CAAC,OAAO;wCAC/B,UAAU,CAAC,IAAM,mBAAmB;gDAClC,KAAK;oDAAE,GAAG,aAAa,GAAG;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAQ;4CAC7D;wCACA,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAsC;;;;;;0CACvD,8OAAC;gCACC,OAAO,aAAa,OAAO;gCAC3B,UAAU,CAAC,IAAM,mBAAmB;wCAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oCAAQ;gCACrE,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;;;;;;;;;;;;;kCAIvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAsC;;;;;;0CACvD,8OAAC;gCACC,OAAO,aAAa,MAAM;gCAC1B,UAAU,CAAC,IAAM,mBAAmB;wCAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oCAAQ;gCACpE,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;;;;;;;;;;;;;kCAIvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,IAAG;gCACH,SAAS,aAAa,WAAW;gCACjC,UAAU,CAAC,IAAM,mBAAmB;wCAAE,aAAa,EAAE,MAAM,CAAC,OAAO;oCAAC;gCACpE,WAAU;;;;;;0CAEZ,8OAAC;gCAAM,SAAQ;gCAAc,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;;;AAOjF", "debugId": null}}, {"offset": {"line": 2188, "column": 0}, "map": {"version": 3, "sources": ["file:///I:/WebDev/component%20generator/component-generator/src/components/generators/ButtonGenerator.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useComponentStore } from '@/store/componentStore';\nimport { ButtonConfig } from '@/types';\nimport ColorPicker from '@/components/ui/ColorPicker';\nimport SizeSelector from '@/components/ui/SizeSelector';\n\nexport default function ButtonGenerator() {\n  const { config, updateConfig } = useComponentStore();\n  const buttonConfig = config as ButtonConfig;\n\n  const updateButtonConfig = (updates: Partial<ButtonConfig>) => {\n    updateConfig(updates);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Size Selection */}\n      <SizeSelector\n        value={buttonConfig.size}\n        onChange={(size) => updateButtonConfig({ size })}\n        label=\"Size\"\n      />\n\n      {/* Colors */}\n      <div className=\"space-y-4\">\n        <h4 className=\"text-sm font-medium text-foreground\">Colors</h4>\n        <div className=\"grid grid-cols-1 gap-4\">\n          <ColorPicker\n            color={buttonConfig.colors.primary}\n            onChange={(color) => updateButtonConfig({\n              colors: { ...buttonConfig.colors, primary: color }\n            })}\n            label=\"Primary Color\"\n          />\n          <ColorPicker\n            color={buttonConfig.colors.text}\n            onChange={(color) => updateButtonConfig({\n              colors: { ...buttonConfig.colors, text: color }\n            })}\n            label=\"Text Color\"\n          />\n          <ColorPicker\n            color={buttonConfig.colors.background}\n            onChange={(color) => updateButtonConfig({\n              colors: { ...buttonConfig.colors, background: color }\n            })}\n            label=\"Background Color\"\n          />\n        </div>\n      </div>\n\n      {/* Button Content */}\n      <div className=\"space-y-3\">\n        <h4 className=\"text-sm font-medium text-foreground\">Content</h4>\n        \n        <div className=\"space-y-2\">\n          <label className=\"block text-sm text-muted-foreground\">Button Text</label>\n          <input\n            type=\"text\"\n            value={buttonConfig.text}\n            onChange={(e) => updateButtonConfig({ text: e.target.value })}\n            className=\"w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n            placeholder=\"Enter button text\"\n          />\n        </div>\n      </div>\n\n      {/* Button Variant */}\n      <div className=\"space-y-3\">\n        <h4 className=\"text-sm font-medium text-foreground\">Variant</h4>\n        \n        <div className=\"grid grid-cols-2 gap-2\">\n          {['primary', 'secondary', 'outline', 'ghost', 'destructive'].map((variant) => (\n            <button\n              key={variant}\n              type=\"button\"\n              onClick={() => updateButtonConfig({ variant: variant as any })}\n              className={`px-3 py-2 text-sm border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-ring ${\n                buttonConfig.variant === variant\n                  ? 'border-primary bg-primary/10 text-primary'\n                  : 'border-border bg-background text-muted-foreground hover:bg-accent hover:text-accent-foreground'\n              }`}\n            >\n              {variant.charAt(0).toUpperCase() + variant.slice(1)}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Icon Configuration */}\n      <div className=\"space-y-3\">\n        <h4 className=\"text-sm font-medium text-foreground\">Icon</h4>\n        \n        <div className=\"flex items-center space-x-2\">\n          <input\n            type=\"checkbox\"\n            id=\"icon-enabled\"\n            checked={buttonConfig.icon.enabled}\n            onChange={(e) => updateButtonConfig({\n              icon: { ...buttonConfig.icon, enabled: e.target.checked }\n            })}\n            className=\"rounded border-border text-primary focus:ring-ring\"\n          />\n          <label htmlFor=\"icon-enabled\" className=\"text-sm text-muted-foreground\">\n            Enable Icon\n          </label>\n        </div>\n\n        {buttonConfig.icon.enabled && (\n          <div className=\"space-y-2\">\n            <div>\n              <label className=\"block text-sm text-muted-foreground\">Icon Position</label>\n              <select\n                value={buttonConfig.icon.position}\n                onChange={(e) => updateButtonConfig({\n                  icon: { ...buttonConfig.icon, position: e.target.value as any }\n                })}\n                className=\"w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n              >\n                <option value=\"left\">Left</option>\n                <option value=\"right\">Right</option>\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm text-muted-foreground\">Icon Name</label>\n              <select\n                value={buttonConfig.icon.name}\n                onChange={(e) => updateButtonConfig({\n                  icon: { ...buttonConfig.icon, name: e.target.value }\n                })}\n                className=\"w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n              >\n                <option value=\"arrow-right\">Arrow Right</option>\n                <option value=\"arrow-left\">Arrow Left</option>\n                <option value=\"plus\">Plus</option>\n                <option value=\"download\">Download</option>\n                <option value=\"external-link\">External Link</option>\n                <option value=\"check\">Check</option>\n                <option value=\"x\">X</option>\n              </select>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Button Options */}\n      <div className=\"space-y-3\">\n        <h4 className=\"text-sm font-medium text-foreground\">Options</h4>\n        \n        <div className=\"space-y-2\">\n          <div className=\"flex items-center space-x-2\">\n            <input\n              type=\"checkbox\"\n              id=\"full-width\"\n              checked={buttonConfig.fullWidth}\n              onChange={(e) => updateButtonConfig({ fullWidth: e.target.checked })}\n              className=\"rounded border-border text-primary focus:ring-ring\"\n            />\n            <label htmlFor=\"full-width\" className=\"text-sm text-muted-foreground\">\n              Full Width\n            </label>\n          </div>\n\n          <div className=\"flex items-center space-x-2\">\n            <input\n              type=\"checkbox\"\n              id=\"disabled\"\n              checked={buttonConfig.disabled}\n              onChange={(e) => updateButtonConfig({ disabled: e.target.checked })}\n              className=\"rounded border-border text-primary focus:ring-ring\"\n            />\n            <label htmlFor=\"disabled\" className=\"text-sm text-muted-foreground\">\n              Disabled State\n            </label>\n          </div>\n\n          <div className=\"flex items-center space-x-2\">\n            <input\n              type=\"checkbox\"\n              id=\"loading\"\n              checked={buttonConfig.loading}\n              onChange={(e) => updateButtonConfig({ loading: e.target.checked })}\n              className=\"rounded border-border text-primary focus:ring-ring\"\n            />\n            <label htmlFor=\"loading\" className=\"text-sm text-muted-foreground\">\n              Loading State\n            </label>\n          </div>\n        </div>\n      </div>\n\n      {/* Style Options */}\n      <div className=\"space-y-3\">\n        <h4 className=\"text-sm font-medium text-foreground\">Style</h4>\n        \n        <div className=\"space-y-2\">\n          <label className=\"block text-sm text-muted-foreground\">Border Radius</label>\n          <select\n            value={buttonConfig.rounded}\n            onChange={(e) => updateButtonConfig({ rounded: e.target.value as any })}\n            className=\"w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n          >\n            <option value=\"none\">None</option>\n            <option value=\"sm\">Small</option>\n            <option value=\"md\">Medium</option>\n            <option value=\"lg\">Large</option>\n            <option value=\"xl\">Extra Large</option>\n            <option value=\"full\">Full (Pill)</option>\n          </select>\n        </div>\n\n        <div className=\"space-y-2\">\n          <label className=\"block text-sm text-muted-foreground\">Shadow</label>\n          <select\n            value={buttonConfig.shadow}\n            onChange={(e) => updateButtonConfig({ shadow: e.target.value as any })}\n            className=\"w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring\"\n          >\n            <option value=\"none\">None</option>\n            <option value=\"sm\">Small</option>\n            <option value=\"md\">Medium</option>\n            <option value=\"lg\">Large</option>\n            <option value=\"xl\">Extra Large</option>\n          </select>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AANA;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD;IACjD,MAAM,eAAe;IAErB,MAAM,qBAAqB,CAAC;QAC1B,aAAa;IACf;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,wIAAA,CAAA,UAAY;gBACX,OAAO,aAAa,IAAI;gBACxB,UAAU,CAAC,OAAS,mBAAmB;wBAAE;oBAAK;gBAC9C,OAAM;;;;;;0BAIR,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,uIAAA,CAAA,UAAW;gCACV,OAAO,aAAa,MAAM,CAAC,OAAO;gCAClC,UAAU,CAAC,QAAU,mBAAmB;wCACtC,QAAQ;4CAAE,GAAG,aAAa,MAAM;4CAAE,SAAS;wCAAM;oCACnD;gCACA,OAAM;;;;;;0CAER,8OAAC,uIAAA,CAAA,UAAW;gCACV,OAAO,aAAa,MAAM,CAAC,IAAI;gCAC/B,UAAU,CAAC,QAAU,mBAAmB;wCACtC,QAAQ;4CAAE,GAAG,aAAa,MAAM;4CAAE,MAAM;wCAAM;oCAChD;gCACA,OAAM;;;;;;0CAER,8OAAC,uIAAA,CAAA,UAAW;gCACV,OAAO,aAAa,MAAM,CAAC,UAAU;gCACrC,UAAU,CAAC,QAAU,mBAAmB;wCACtC,QAAQ;4CAAE,GAAG,aAAa,MAAM;4CAAE,YAAY;wCAAM;oCACtD;gCACA,OAAM;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAsC;;;;;;0CACvD,8OAAC;gCACC,MAAK;gCACL,OAAO,aAAa,IAAI;gCACxB,UAAU,CAAC,IAAM,mBAAmB;wCAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oCAAC;gCAC3D,WAAU;gCACV,aAAY;;;;;;;;;;;;;;;;;;0BAMlB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAW;4BAAa;4BAAW;4BAAS;yBAAc,CAAC,GAAG,CAAC,CAAC,wBAChE,8OAAC;gCAEC,MAAK;gCACL,SAAS,IAAM,mBAAmB;wCAAE,SAAS;oCAAe;gCAC5D,WAAW,CAAC,sGAAsG,EAChH,aAAa,OAAO,KAAK,UACrB,8CACA,kGACJ;0CAED,QAAQ,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,KAAK,CAAC;+BAT5C;;;;;;;;;;;;;;;;0BAgBb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,IAAG;gCACH,SAAS,aAAa,IAAI,CAAC,OAAO;gCAClC,UAAU,CAAC,IAAM,mBAAmB;wCAClC,MAAM;4CAAE,GAAG,aAAa,IAAI;4CAAE,SAAS,EAAE,MAAM,CAAC,OAAO;wCAAC;oCAC1D;gCACA,WAAU;;;;;;0CAEZ,8OAAC;gCAAM,SAAQ;gCAAe,WAAU;0CAAgC;;;;;;;;;;;;oBAKzE,aAAa,IAAI,CAAC,OAAO,kBACxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAsC;;;;;;kDACvD,8OAAC;wCACC,OAAO,aAAa,IAAI,CAAC,QAAQ;wCACjC,UAAU,CAAC,IAAM,mBAAmB;gDAClC,MAAM;oDAAE,GAAG,aAAa,IAAI;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAQ;4CAChE;wCACA,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;;;;;;;;;;;;;0CAI1B,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAsC;;;;;;kDACvD,8OAAC;wCACC,OAAO,aAAa,IAAI,CAAC,IAAI;wCAC7B,UAAU,CAAC,IAAM,mBAAmB;gDAClC,MAAM;oDAAE,GAAG,aAAa,IAAI;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACrD;wCACA,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAc;;;;;;0DAC5B,8OAAC;gDAAO,OAAM;0DAAa;;;;;;0DAC3B,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,8OAAC;gDAAO,OAAM;0DAAgB;;;;;;0DAC9B,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,SAAS,aAAa,SAAS;wCAC/B,UAAU,CAAC,IAAM,mBAAmB;gDAAE,WAAW,EAAE,MAAM,CAAC,OAAO;4CAAC;wCAClE,WAAU;;;;;;kDAEZ,8OAAC;wCAAM,SAAQ;wCAAa,WAAU;kDAAgC;;;;;;;;;;;;0CAKxE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,SAAS,aAAa,QAAQ;wCAC9B,UAAU,CAAC,IAAM,mBAAmB;gDAAE,UAAU,EAAE,MAAM,CAAC,OAAO;4CAAC;wCACjE,WAAU;;;;;;kDAEZ,8OAAC;wCAAM,SAAQ;wCAAW,WAAU;kDAAgC;;;;;;;;;;;;0CAKtE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,SAAS,aAAa,OAAO;wCAC7B,UAAU,CAAC,IAAM,mBAAmB;gDAAE,SAAS,EAAE,MAAM,CAAC,OAAO;4CAAC;wCAChE,WAAU;;;;;;kDAEZ,8OAAC;wCAAM,SAAQ;wCAAU,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;0BAQzE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAsC;;;;;;0CACvD,8OAAC;gCACC,OAAO,aAAa,OAAO;gCAC3B,UAAU,CAAC,IAAM,mBAAmB;wCAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oCAAQ;gCACrE,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAO;;;;;;;;;;;;;;;;;;kCAIzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAsC;;;;;;0CACvD,8OAAC;gCACC,OAAO,aAAa,MAAM;gCAC1B,UAAU,CAAC,IAAM,mBAAmB;wCAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oCAAQ;gCACpE,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,8OAAC;wCAAO,OAAM;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM/B", "debugId": null}}, {"offset": {"line": 2868, "column": 0}, "map": {"version": 3, "sources": ["file:///I:/WebDev/component%20generator/component-generator/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect } from \"react\";\nimport { Moon, Sun, RotateCcw } from \"lucide-react\";\nimport { useComponentStore } from \"@/store/componentStore\";\nimport { generateComponentCode } from \"@/lib/codeGenerator\";\nimport ComponentTypeSelector from \"@/components/ui/ComponentTypeSelector\";\nimport PreviewContainer from \"@/components/ui/PreviewContainer\";\nimport CodeBlock from \"@/components/ui/CodeBlock\";\nimport ComponentPreview from \"@/components/ui/ComponentPreview\";\nimport NavbarGenerator from \"@/components/generators/NavbarGenerator\";\nimport ButtonGenerator from \"@/components/generators/ButtonGenerator\";\n\nexport default function Home() {\n  const {\n    currentComponent,\n    config,\n    generatedCode,\n    previewMode,\n    darkMode,\n    setCurrentComponent,\n    setGeneratedCode,\n    setPreviewMode,\n    toggleDarkMode,\n    resetConfig,\n  } = useComponentStore();\n\n  // Generate code whenever config changes\n  useEffect(() => {\n    const code = generateComponentCode(config);\n    setGeneratedCode(code);\n  }, [config, setGeneratedCode]);\n\n  const renderGenerator = () => {\n    switch (currentComponent) {\n      case \"navbar\":\n        return <NavbarGenerator />;\n      case \"button\":\n        return <ButtonGenerator />;\n      case \"card\":\n        return (\n          <div className=\"p-4 text-center text-muted-foreground\">\n            Card generator coming soon...\n          </div>\n        );\n      case \"form\":\n        return (\n          <div className=\"p-4 text-center text-muted-foreground\">\n            Form generator coming soon...\n          </div>\n        );\n      default:\n        return (\n          <div className=\"p-4 text-center text-muted-foreground\">\n            Select a component type to get started\n          </div>\n        );\n    }\n  };\n\n  const renderPreview = () => {\n    return <ComponentPreview config={config} />;\n  };\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Header */}\n      <header className=\"border-b border-border bg-card\">\n        <div className=\"container mx-auto px-4 py-6\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-foreground\">\n                Component Generator\n              </h1>\n              <p className=\"text-muted-foreground mt-1\">\n                Create beautiful React components with Tailwind CSS\n              </p>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={resetConfig}\n                className=\"flex items-center space-x-2 px-3 py-2 text-sm bg-background border border-border rounded-md hover:bg-accent hover:text-accent-foreground transition-colors\"\n                title=\"Reset configuration\"\n              >\n                <RotateCcw className=\"w-4 h-4\" />\n                <span className=\"hidden sm:inline\">Reset</span>\n              </button>\n\n              <button\n                onClick={toggleDarkMode}\n                className=\"flex items-center space-x-2 px-3 py-2 text-sm bg-background border border-border rounded-md hover:bg-accent hover:text-accent-foreground transition-colors\"\n                title={\n                  darkMode ? \"Switch to light mode\" : \"Switch to dark mode\"\n                }\n              >\n                {darkMode ? (\n                  <Sun className=\"w-4 h-4\" />\n                ) : (\n                  <Moon className=\"w-4 h-4\" />\n                )}\n                <span className=\"hidden sm:inline\">\n                  {darkMode ? \"Light\" : \"Dark\"}\n                </span>\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main content */}\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Left sidebar - Component selection and configuration */}\n          <div className=\"lg:col-span-1 space-y-6\">\n            <ComponentTypeSelector\n              value={currentComponent}\n              onChange={setCurrentComponent}\n            />\n\n            <div className=\"bg-card border border-border rounded-lg p-6\">\n              <h3 className=\"text-lg font-semibold text-foreground mb-4\">\n                Configuration\n              </h3>\n              {renderGenerator()}\n            </div>\n          </div>\n\n          {/* Right content - Preview and code */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            <PreviewContainer mode={previewMode} onModeChange={setPreviewMode}>\n              {renderPreview()}\n            </PreviewContainer>\n\n            <CodeBlock\n              code={generatedCode}\n              filename={`${currentComponent.charAt(0).toUpperCase() + currentComponent.slice(1)}.tsx`}\n            />\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,EACJ,gBAAgB,EAChB,MAAM,EACN,aAAa,EACb,WAAW,EACX,QAAQ,EACR,mBAAmB,EACnB,gBAAgB,EAChB,cAAc,EACd,cAAc,EACd,WAAW,EACZ,GAAG,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD;IAEpB,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,OAAO,CAAA,GAAA,2HAAA,CAAA,wBAAqB,AAAD,EAAE;QACnC,iBAAiB;IACnB,GAAG;QAAC;QAAQ;KAAiB;IAE7B,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,mJAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,mJAAA,CAAA,UAAe;;;;;YACzB,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BAAwC;;;;;;YAI3D,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BAAwC;;;;;;YAI3D;gBACE,qBACE,8OAAC;oBAAI,WAAU;8BAAwC;;;;;;QAI7D;IACF;IAEA,MAAM,gBAAgB;QACpB,qBAAO,8OAAC,4IAAA,CAAA,UAAgB;YAAC,QAAQ;;;;;;IACnC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDAGnD,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAK5C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,OAAM;;0DAEN,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;gDAAK,WAAU;0DAAmB;;;;;;;;;;;;kDAGrC,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,OACE,WAAW,yBAAyB;;4CAGrC,yBACC,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;qEAEf,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAElB,8OAAC;gDAAK,WAAU;0DACb,WAAW,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlC,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iJAAA,CAAA,UAAqB;oCACpB,OAAO;oCACP,UAAU;;;;;;8CAGZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA6C;;;;;;wCAG1D;;;;;;;;;;;;;sCAKL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4IAAA,CAAA,UAAgB;oCAAC,MAAM;oCAAa,cAAc;8CAChD;;;;;;8CAGH,8OAAC,qIAAA,CAAA,UAAS;oCACR,MAAM;oCACN,UAAU,GAAG,iBAAiB,MAAM,CAAC,GAAG,WAAW,KAAK,iBAAiB,KAAK,CAAC,GAAG,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrG", "debugId": null}}]}