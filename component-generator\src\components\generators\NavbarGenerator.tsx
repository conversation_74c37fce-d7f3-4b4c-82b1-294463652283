'use client';

import React from 'react';
import { useComponentStore } from '@/store/componentStore';
import { NavbarConfig } from '@/types';
import ColorPicker from '@/components/ui/ColorPicker';
import SizeSelector from '@/components/ui/SizeSelector';

export default function NavbarGenerator() {
  const { config, updateConfig } = useComponentStore();
  const navbarConfig = config as NavbarConfig;

  const updateNavbarConfig = (updates: Partial<NavbarConfig>) => {
    updateConfig(updates);
  };

  return (
    <div className="space-y-6">
      {/* Size Selection */}
      <SizeSelector
        value={navbarConfig.size}
        onChange={(size) => updateNavbarConfig({ size })}
        label="Size"
      />

      {/* Colors */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-foreground">Colors</h4>
        <div className="grid grid-cols-1 gap-4">
          <ColorPicker
            color={navbarConfig.colors.primary}
            onChange={(color) => updateNavbarConfig({
              colors: { ...navbarConfig.colors, primary: color }
            })}
            label="Primary Color"
          />
          <ColorPicker
            color={navbarConfig.colors.background}
            onChange={(color) => updateNavbarConfig({
              colors: { ...navbarConfig.colors, background: color }
            })}
            label="Background Color"
          />
          <ColorPicker
            color={navbarConfig.colors.text}
            onChange={(color) => updateNavbarConfig({
              colors: { ...navbarConfig.colors, text: color }
            })}
            label="Text Color"
          />
        </div>
      </div>

      {/* Layout Options */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-foreground">Layout</h4>
        
        <div className="space-y-2">
          <label className="block text-sm text-muted-foreground">Position</label>
          <select
            value={navbarConfig.position}
            onChange={(e) => updateNavbarConfig({ position: e.target.value as any })}
            className="w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
          >
            <option value="static">Static</option>
            <option value="fixed">Fixed</option>
            <option value="sticky">Sticky</option>
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm text-muted-foreground">Navigation Alignment</label>
          <select
            value={navbarConfig.navigation.alignment}
            onChange={(e) => updateNavbarConfig({
              navigation: { ...navbarConfig.navigation, alignment: e.target.value as any }
            })}
            className="w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
          >
            <option value="left">Left</option>
            <option value="center">Center</option>
            <option value="right">Right</option>
            <option value="space-between">Space Between</option>
          </select>
        </div>
      </div>

      {/* Logo Configuration */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-foreground">Logo</h4>
        
        <div className="space-y-2">
          <label className="block text-sm text-muted-foreground">Logo Text</label>
          <input
            type="text"
            value={navbarConfig.logo.text}
            onChange={(e) => updateNavbarConfig({
              logo: { ...navbarConfig.logo, text: e.target.value }
            })}
            className="w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
            placeholder="Enter logo text"
          />
        </div>

        <div className="space-y-2">
          <label className="block text-sm text-muted-foreground">Logo Position</label>
          <select
            value={navbarConfig.logo.position}
            onChange={(e) => updateNavbarConfig({
              logo: { ...navbarConfig.logo, position: e.target.value as any }
            })}
            className="w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
          >
            <option value="left">Left</option>
            <option value="center">Center</option>
            <option value="right">Right</option>
          </select>
        </div>
      </div>

      {/* CTA Button */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-foreground">Call to Action</h4>
        
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="cta-enabled"
            checked={navbarConfig.cta.enabled}
            onChange={(e) => updateNavbarConfig({
              cta: { ...navbarConfig.cta, enabled: e.target.checked }
            })}
            className="rounded border-border text-primary focus:ring-ring"
          />
          <label htmlFor="cta-enabled" className="text-sm text-muted-foreground">
            Enable CTA Button
          </label>
        </div>

        {navbarConfig.cta.enabled && (
          <div className="space-y-2">
            <div>
              <label className="block text-sm text-muted-foreground">Button Text</label>
              <input
                type="text"
                value={navbarConfig.cta.text}
                onChange={(e) => updateNavbarConfig({
                  cta: { ...navbarConfig.cta, text: e.target.value }
                })}
                className="w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
                placeholder="Enter button text"
              />
            </div>

            <div>
              <label className="block text-sm text-muted-foreground">Button Variant</label>
              <select
                value={navbarConfig.cta.variant}
                onChange={(e) => updateNavbarConfig({
                  cta: { ...navbarConfig.cta, variant: e.target.value as any }
                })}
                className="w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="primary">Primary</option>
                <option value="secondary">Secondary</option>
                <option value="outline">Outline</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Style Options */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-foreground">Style</h4>
        
        <div className="space-y-2">
          <label className="block text-sm text-muted-foreground">Border Radius</label>
          <select
            value={navbarConfig.rounded}
            onChange={(e) => updateNavbarConfig({ rounded: e.target.value as any })}
            className="w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
          >
            <option value="none">None</option>
            <option value="sm">Small</option>
            <option value="md">Medium</option>
            <option value="lg">Large</option>
            <option value="xl">Extra Large</option>
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm text-muted-foreground">Shadow</label>
          <select
            value={navbarConfig.shadow}
            onChange={(e) => updateNavbarConfig({ shadow: e.target.value as any })}
            className="w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
          >
            <option value="none">None</option>
            <option value="sm">Small</option>
            <option value="md">Medium</option>
            <option value="lg">Large</option>
            <option value="xl">Extra Large</option>
          </select>
        </div>

        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="transparent"
            checked={navbarConfig.transparent}
            onChange={(e) => updateNavbarConfig({ transparent: e.target.checked })}
            className="rounded border-border text-primary focus:ring-ring"
          />
          <label htmlFor="transparent" className="text-sm text-muted-foreground">
            Transparent Background
          </label>
        </div>
      </div>
    </div>
  );
}
