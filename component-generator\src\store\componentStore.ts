import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { ComponentGeneratorState, ComponentConfig, ComponentType, NavbarConfig, ButtonConfig } from '@/types';

// Default configurations for each component type
const defaultNavbarConfig: NavbarConfig = {
  type: 'navbar',
  size: 'md',
  colors: {
    primary: '#3b82f6',
    secondary: '#64748b',
    background: '#ffffff',
    text: '#1f2937',
    accent: '#f59e0b',
  },
  rounded: 'none',
  shadow: 'sm',
  font: 'Inter',
  layout: 'horizontal',
  position: 'static',
  transparent: false,
  logo: {
    text: 'Logo',
    position: 'left',
  },
  navigation: {
    items: [
      { label: 'Home', href: '/', active: true },
      { label: 'About', href: '/about' },
      { label: 'Services', href: '/services' },
      { label: 'Contact', href: '/contact' },
    ],
    alignment: 'center',
  },
  cta: {
    enabled: true,
    text: 'Get Started',
    variant: 'primary',
  },
};

const defaultButtonConfig: ButtonConfig = {
  type: 'button',
  size: 'md',
  colors: {
    primary: '#3b82f6',
    secondary: '#64748b',
    background: '#ffffff',
    text: '#ffffff',
    accent: '#f59e0b',
  },
  rounded: 'md',
  shadow: 'sm',
  font: 'Inter',
  variant: 'primary',
  text: 'Click me',
  icon: {
    enabled: false,
    position: 'left',
    name: 'arrow-right',
  },
  fullWidth: false,
  disabled: false,
  loading: false,
};

const getDefaultConfig = (type: ComponentType): ComponentConfig => {
  switch (type) {
    case 'navbar':
      return defaultNavbarConfig;
    case 'button':
      return defaultButtonConfig;
    case 'card':
      return {
        type: 'card',
        size: 'md',
        colors: defaultNavbarConfig.colors,
        rounded: 'lg',
        shadow: 'md',
        font: 'Inter',
        layout: 'simple',
        content: {
          title: 'Card Title',
          description: 'This is a sample card description that explains what this card is about.',
          actions: [
            { label: 'Learn More', variant: 'primary' },
            { label: 'Cancel', variant: 'outline' },
          ],
        },
        hover: {
          enabled: true,
          effect: 'lift',
        },
      };
    case 'form':
      return {
        type: 'form',
        size: 'md',
        colors: defaultNavbarConfig.colors,
        rounded: 'md',
        shadow: 'sm',
        font: 'Inter',
        layout: 'vertical',
        fields: [
          { type: 'text', label: 'Name', placeholder: 'Enter your name', required: true },
          { type: 'email', label: 'Email', placeholder: 'Enter your email', required: true },
          { type: 'textarea', label: 'Message', placeholder: 'Enter your message', required: false },
        ],
        submitButton: {
          text: 'Submit',
          variant: 'primary',
          fullWidth: true,
        },
      };
    default:
      return defaultNavbarConfig;
  }
};

export const useComponentStore = create<ComponentGeneratorState>()(
  devtools(
    persist(
      (set, get) => ({
        currentComponent: 'navbar',
        config: defaultNavbarConfig,
        generatedCode: '',
        previewMode: 'desktop',
        darkMode: false,

        setCurrentComponent: (type: ComponentType) => {
          const newConfig = getDefaultConfig(type);
          set({
            currentComponent: type,
            config: newConfig,
            generatedCode: '',
          });
        },

        updateConfig: (newConfig: Partial<ComponentConfig>) => {
          const currentConfig = get().config;
          set({
            config: { ...currentConfig, ...newConfig } as ComponentConfig,
          });
        },

        setGeneratedCode: (code: string) => {
          set({ generatedCode: code });
        },

        setPreviewMode: (mode: 'desktop' | 'tablet' | 'mobile') => {
          set({ previewMode: mode });
        },

        toggleDarkMode: () => {
          set((state) => ({ darkMode: !state.darkMode }));
        },

        resetConfig: () => {
          const { currentComponent } = get();
          const defaultConfig = getDefaultConfig(currentComponent);
          set({
            config: defaultConfig,
            generatedCode: '',
          });
        },
      }),
      {
        name: 'component-generator-storage',
        partialize: (state) => ({
          currentComponent: state.currentComponent,
          config: state.config,
          darkMode: state.darkMode,
        }),
      }
    ),
    {
      name: 'component-generator',
    }
  )
);
