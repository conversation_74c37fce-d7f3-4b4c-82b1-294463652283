# React + Tailwind CSS Component Generator

A production-grade web application that allows users to create, customize, and export beautiful React components with Tailwind CSS. Built with Next.js 15, TypeScript, and modern development practices.

## 🚀 Features

### Core Functionality

- **Component Generation**: Create production-ready React components with clean, semantic code
- **Live Preview**: Real-time preview of components as you customize them
- **Responsive Design**: Test components across desktop, tablet, and mobile breakpoints
- **Code Export**: Copy to clipboard or download generated components as `.tsx` files

### Supported Components

- **Navbar**: Customizable navigation bars with logo, menu items, and CTA buttons
- **Button**: Interactive buttons with multiple variants, sizes, and states
- **Card**: Content containers (coming soon)
- **Form**: Input forms with validation (coming soon)

### Customization Options

- **Colors**: Primary, secondary, background, text, and accent colors with color picker
- **Sizes**: Small, medium, large, and extra-large sizing options
- **Styling**: Border radius, shadows, transparency, and positioning
- **Layout**: Flexible layout options specific to each component type
- **States**: Hover, active, disabled, and loading states

### Enhanced Features

- **Dark Mode**: Toggle between light and dark themes
- **Preset Management**: Save, load, export, and import component configurations
- **Reset Functionality**: Quickly reset to default configurations
- **Responsive Preview**: Test components at different screen sizes
- **State Persistence**: Configurations saved in localStorage

## 🛠 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom design system
- **State Management**: Zustand with persistence
- **UI Components**: Custom components with Lucide React icons
- **Color Picker**: React Color
- **Code Editor**: Monaco Editor integration ready
- **Fonts**: Inter and JetBrains Mono from Google Fonts

## 📁 Project Structure

```text
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # Global styles and design system
│   ├── layout.tsx         # Root layout with theme provider
│   └── page.tsx           # Main application page
├── components/
│   ├── generators/        # Component-specific generators
│   │   ├── NavbarGenerator.tsx
│   │   └── ButtonGenerator.tsx
│   ├── providers/         # Context providers
│   │   └── ThemeProvider.tsx
│   └── ui/               # Reusable UI components
│       ├── ColorPicker.tsx
│       ├── SizeSelector.tsx
│       ├── CodeBlock.tsx
│       ├── ComponentPreview.tsx
│       ├── ComponentTypeSelector.tsx
│       ├── PreviewContainer.tsx
│       └── PresetManager.tsx
├── lib/                   # Utility functions
│   ├── utils.ts          # General utilities
│   └── codeGenerator.ts  # Component code generation
├── store/                # State management
│   └── componentStore.ts # Zustand store
└── types/                # TypeScript definitions
    └── index.ts          # Component and state types
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd component-generator
   ```

2. **Install dependencies**

   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Run the development server**

   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎯 Usage

1. **Select Component Type**: Choose from available component types (Navbar, Button, etc.)
2. **Customize**: Use the configuration panel to adjust colors, sizes, and styling options
3. **Preview**: See real-time changes in the preview panel with responsive breakpoints
4. **Export**: Copy the generated code or download as a file
5. **Save Presets**: Save your configurations for future use
6. **Toggle Theme**: Switch between light and dark modes

## 🏗 Architecture

### State Management

- **Zustand Store**: Centralized state management with persistence
- **Type Safety**: Full TypeScript support with strict typing
- **Reactive Updates**: Automatic code generation on configuration changes

### Code Generation

- **Template-based**: Clean, maintainable code generation
- **Production-ready**: Generated code follows React and Tailwind best practices
- **Customizable**: Easy to extend with new component types

### Design System

- **CSS Custom Properties**: Flexible theming system
- **Tailwind Integration**: Seamless integration with Tailwind CSS
- **Responsive**: Mobile-first responsive design

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy with zero configuration

### Other Platforms

The application can be deployed to any platform that supports Next.js:

- Netlify
- Railway
- AWS Amplify
- DigitalOcean App Platform

### Build for Production

```bash
npm run build
npm start
```

## 🔧 Development

### Adding New Components

1. Create component configuration types in `src/types/index.ts`
2. Add default configuration in `src/store/componentStore.ts`
3. Create generator component in `src/components/generators/`
4. Add code generation logic in `src/lib/codeGenerator.ts`
5. Update preview component in `src/components/ui/ComponentPreview.tsx`

### Extending Customization Options

1. Update component configuration interfaces
2. Add new UI controls in generator components
3. Update code generation templates
4. Test with live preview

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
