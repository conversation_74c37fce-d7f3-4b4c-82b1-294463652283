
define([], function () {
/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.52.2(404545bded1df6ffa41ea0af4e8ddb219018c6c1)
 * Released under the MIT license
 * https://github.com/microsoft/vscode/blob/main/LICENSE.txt
 *-----------------------------------------------------------*/globalThis._VSCODE_NLS_MESSAGES=["{0} ({1})","entr\xE9e","Respecter la casse","Mot entier","Utiliser une expression r\xE9guli\xE8re","entr\xE9e","Pr\xE9server la casse","Inspectez ceci dans l\u2019affichage accessible avec {0}.","Inspectez ceci dans l\u2019affichage accessible via la commande Open Accessible View qui ne peut pas \xEAtre d\xE9clench\xE9e via une combinaison de touches pour l\u2019instant.","Erreur\xA0: {0}","Avertissement\xA0: {0}","Info\xA0: {0}"," ou {0} pour l'histoire"," ({0} pour l'histoire)","Entr\xE9e effac\xE9e","Ind\xE9pendant","Zone de s\xE9lection","Plus d'actions...","Filtrer","Correspondance approximative","Type \xE0 filtrer","Entrer le texte \xE0 rechercher","Entrer le texte \xE0 rechercher","Fermer","Aucun r\xE9sultat","Aucun r\xE9sultat trouv\xE9.",null,"(vide)","{0}: {1}","Une erreur syst\xE8me s'est produite ({0})","Une erreur inconnue s\u2019est produite. Veuillez consulter le journal pour plus de d\xE9tails.","Une erreur inconnue s\u2019est produite. Veuillez consulter le journal pour plus de d\xE9tails.","{0} ({1}\xA0erreurs au total)","Une erreur inconnue s\u2019est produite. Veuillez consulter le journal pour plus de d\xE9tails.","Ctrl","Maj","Alt","Windows","Ctrl","Maj","Alt","Super","Contr\xF4le","Maj","Option","Commande","Contr\xF4le","Maj","Alt","Windows","Contr\xF4le","Maj","Alt","Super",null,null,null,null,null,"Aligner par rapport \xE0 la fin m\xEAme en cas de passage \xE0 des lignes plus longues","Aligner par rapport \xE0 la fin m\xEAme en cas de passage \xE0 des lignes plus longues","Curseurs secondaires supprim\xE9s","Ann&&uler","Annuler","&&R\xE9tablir","R\xE9tablir","&&S\xE9lectionner tout","Tout s\xE9lectionner","Maintenez la touche {0} enfonc\xE9e pour pointer avec la souris","Chargement...","Le nombre de curseurs a \xE9t\xE9 limit\xE9 \xE0 {0}. Envisagez d\u2019utiliser [rechercher et remplacer](https://code.visualstudio.com/docs/editor/codebasics#_find-and-replace) pour les modifications plus importantes ou augmentez la limite du nombre de curseurs multiples du param\xE8tre.","Augmenter la limite de curseurs multiples","Activer/d\xE9sactiver r\xE9duire les r\xE9gions inchang\xE9es","Activer/d\xE9sactiver l\u2019affichage des blocs de code d\xE9plac\xE9s","Activer/d\xE9sactiver Utiliser la vue inline lorsque l'espace est limit\xE9","\xC9diteur de diff\xE9rences","Changer de c\xF4t\xE9","Quitter Comparer le d\xE9placement","R\xE9duire toutes les r\xE9gions inchang\xE9es","Afficher toutes les r\xE9gions inchang\xE9es","Restaurer","Visionneuse Diff accessible","Acc\xE9der \xE0 la diff\xE9rence suivante","Acc\xE9der la diff\xE9rence pr\xE9c\xE9dente","Ic\xF4ne \xAB Ins\xE9rer \xBB dans la visionneuse diff accessible.","Ic\xF4ne \xAB Supprimer \xBB dans la visionneuse diff accessible.","Ic\xF4ne de \xAB Fermer \xBB dans la visionneuse diff accessible.","Fermer","Visionneuse diff accessible. Utilisez les fl\xE8ches haut et bas pour naviguer.","aucune ligne chang\xE9e","1\xA0ligne chang\xE9e","{0}\xA0lignes chang\xE9es","Diff\xE9rence\xA0{0} sur\xA0{1}\xA0: ligne d'origine {2}, {3}, ligne modifi\xE9e {4}, {5}","vide","{0} ligne inchang\xE9e {1}","{0}\xA0ligne d'origine {1}\xA0ligne modifi\xE9e {2}","+ {0}\xA0ligne modifi\xE9e {1}","- {0} ligne d'origine {1}"," utilisez {0} pour ouvrir l\u2019aide sur l\u2019accessibilit\xE9.","Copier les lignes supprim\xE9es","Copier la ligne supprim\xE9e","Copier les lignes modifi\xE9es","Copier la ligne modifi\xE9e","Copier la ligne supprim\xE9e ({0})","Copier la ligne modifi\xE9e ({0})","Annuler la modification","Utiliser la vue inline lorsque l'espace est limit\xE9","Afficher les blocs de code d\xE9plac\xE9s","R\xE9tablir le bloc","R\xE9tablir la s\xE9lection","Ouvrir la visionneuse diff accessible","Replier la r\xE9gion inchang\xE9e","{0} lignes masqu\xE9es","Cliquez ou faites glisser pour afficher plus d'\xE9l\xE9ments au-dessus","Afficher la r\xE9gion inchang\xE9e","Cliquez ou faites glisser pour afficher plus d'\xE9l\xE9ments en dessous","{0} lignes masqu\xE9es","Double-cliquer pour d\xE9plier","Code d\xE9plac\xE9 avec des modifications vers la ligne {0}-{1}","Code d\xE9plac\xE9 avec des modifications \xE0 partir de la ligne {0}-{1}","Code d\xE9plac\xE9 vers la ligne {0}-{1}","Code d\xE9plac\xE9 \xE0 partir de la ligne {0}-{1}","R\xE9tablir les modifications s\xE9lectionn\xE9es","R\xE9tablir la modification","Couleur de bordure du texte d\xE9plac\xE9 dans l\u2019\xE9diteur de diff.","Couleur de bordure active du texte d\xE9plac\xE9 dans l\u2019\xE9diteur de diff\xE9rences.","Couleur de l\u2019ombre autour des widgets de r\xE9gion inchang\xE9s.","\xC9l\xE9ment d\xE9coratif de ligne pour les insertions dans l'\xE9diteur de diff\xE9rences.","\xC9l\xE9ment d\xE9coratif de ligne pour les suppressions dans l'\xE9diteur de diff\xE9rences.","Couleur d\u2019arri\xE8re-plan de l\u2019en-t\xEAte de l\u2019\xE9diteur de diff\xE9rences","Couleur d\u2019arri\xE8re-plan de l\u2019\xE9diteur de diff\xE9rences de fichiers multiples","Couleur de bordure de l\u2019\xE9diteur de diff\xE9rences de fichiers multiples","Aucun fichier modifi\xE9","\xC9diteur","Le nombre d\u2019espaces auxquels une tabulation est \xE9gale. Ce param\xE8tre est substitu\xE9 bas\xE9 sur le contenu du fichier lorsque {0} est activ\xE9.",'Nombre d\u2019espaces utilis\xE9s pour la mise en retrait ou `"tabSize"` pour utiliser la valeur de `#editor.tabSize#`. Ce param\xE8tre est remplac\xE9 en fonction du contenu du fichier quand `#editor.detectIndentation#` est activ\xE9.',"Espaces ins\xE9r\xE9s quand vous appuyez sur la touche Tab. Ce param\xE8tre est remplac\xE9 en fonction du contenu du fichier quand {0} est activ\xE9.","Contr\xF4le si {0} et {1} sont automatiquement d\xE9tect\xE9s lors de l\u2019ouverture d\u2019un fichier en fonction de son contenu.","Supprimer l'espace blanc de fin ins\xE9r\xE9 automatiquement.","Traitement sp\xE9cial des fichiers volumineux pour d\xE9sactiver certaines fonctionnalit\xE9s utilisant beaucoup de m\xE9moire.","D\xE9sactivez les suggestions bas\xE9es sur Word.","Sugg\xE8re uniquement des mots dans le document actif.","Sugg\xE8re des mots dans tous les documents ouverts du m\xEAme langage.","Sugg\xE8re des mots dans tous les documents ouverts.","Contr\xF4le si les compl\xE9tions doivent \xEAtre calcul\xE9es en fonction des mots du document et \xE0 partir de quels documents elles sont calcul\xE9es.","Coloration s\xE9mantique activ\xE9e pour tous les th\xE8mes de couleur.","Coloration s\xE9mantique d\xE9sactiv\xE9e pour tous les th\xE8mes de couleur.","La coloration s\xE9mantique est configur\xE9e par le param\xE8tre 'semanticHighlighting' du th\xE8me de couleur actuel.","Contr\xF4le si semanticHighlighting est affich\xE9 pour les langages qui le prennent en charge.","Maintenir les \xE9diteurs d'aper\xE7u ouverts m\xEAme si l'utilisateur double-clique sur son contenu ou appuie sur la touche \xC9chap.","Les lignes plus longues que cette valeur ne sont pas tokenis\xE9es pour des raisons de performances","Contr\xF4le si la cr\xE9ation de jetons doit se produire de mani\xE8re asynchrone sur un worker web.","Contr\xF4le si la cr\xE9ation de jetons asynchrones doit \xEAtre journalis\xE9e. Pour le d\xE9bogage uniquement.","Contr\xF4le si la segmentation du texte en unit\xE9s lexicales asynchrones doit \xEAtre v\xE9rifi\xE9e par rapport \xE0 la segmentation du texte en unit\xE9s lexicales en arri\xE8re-plan h\xE9rit\xE9e. Peut ralentir la segmentation du texte en unit\xE9s lexicales. Pour le d\xE9bogage uniquement.","Contr\xF4le si l\u2019analyse de sitter (syst\xE8me) d\u2019arborescence doit \xEAtre activ\xE9e et la t\xE9l\xE9m\xE9trie collect\xE9e. La d\xE9finition de `editor.experimental.preferTreeSitter` pour des langages sp\xE9cifiques est prioritaire.","D\xE9finit les symboles de type crochet qui augmentent ou diminuent le retrait.","S\xE9quence de cha\xEEnes ou de caract\xE8res de crochets ouvrants.","S\xE9quence de cha\xEEnes ou de caract\xE8res de crochets fermants.","D\xE9finit les paires de crochets qui sont coloris\xE9es par leur niveau d\u2019imbrication si la colorisation des paires de crochets est activ\xE9e.","S\xE9quence de cha\xEEnes ou de caract\xE8res de crochets ouvrants.","S\xE9quence de cha\xEEnes ou de caract\xE8res de crochets fermants.","D\xE9lai d'expiration en millisecondes avant annulation du calcul de diff. Utilisez\xA00 pour supprimer le d\xE9lai d'expiration.","Taille de fichier maximale en Mo pour laquelle calculer les diff\xE9rences. Utilisez 0 pour ne pas avoir de limite.","Contr\xF4le si l'\xE9diteur de diff\xE9rences affiche les diff\xE9rences en mode c\xF4te \xE0 c\xF4te ou inline.","Si l'\xE9diteur de diff\xE9rences est moins large que cette valeur, la vue inline est utilis\xE9e.","Si cette option est activ\xE9e et que la largeur de l'\xE9diteur est trop \xE9troite, la vue inline est utilis\xE9e.","Lorsqu\u2019il est activ\xE9, l\u2019\xE9diteur de diff\xE9rences affiche des fl\xE8ches dans sa marge de glyphe pour r\xE9tablir les modifications.","Lorsque cette option est activ\xE9e, l\u2019\xE9diteur de diff\xE9rences affiche une marge sp\xE9ciale pour les actions de r\xE9tablissement et d\u2019index.","Quand il est activ\xE9, l'\xE9diteur de diff\xE9rences ignore les changements d'espace blanc de d\xE9but ou de fin.","Contr\xF4le si l'\xE9diteur de diff\xE9rences affiche les indicateurs +/- pour les changements ajout\xE9s/supprim\xE9s .","Contr\xF4le si l'\xE9diteur affiche CodeLens.","Le retour automatique \xE0 la ligne n'est jamais effectu\xE9.","Le retour automatique \xE0 la ligne s'effectue en fonction de la largeur de la fen\xEAtre d'affichage.","Le retour automatique \xE0 la ligne d\xE9pend du param\xE8tre {0}.","Utilise l\u2019algorithme de comparaison h\xE9rit\xE9.","Utilise l\u2019algorithme de comparaison avanc\xE9.","Contr\xF4le si l'\xE9diteur de diff\xE9rences affiche les r\xE9gions inchang\xE9es.","Contr\xF4le le nombre de lignes utilis\xE9es pour les r\xE9gions inchang\xE9es.","Contr\xF4le le nombre de lignes utilis\xE9es comme minimum pour les r\xE9gions inchang\xE9es.","Contr\xF4le le nombre de lignes utilis\xE9es comme contexte lors de la comparaison des r\xE9gions inchang\xE9es.","Contr\xF4le si l\u2019\xE9diteur de diff\xE9rences doit afficher les d\xE9placements de code d\xE9tect\xE9s.","Contr\xF4le si l\u2019\xE9diteur de diff\xE9rences affiche des d\xE9corations vides pour voir o\xF9 les caract\xE8res ont \xE9t\xE9 ins\xE9r\xE9s ou supprim\xE9s.","Si cette option est activ\xE9e et que l\u2019\xE9diteur utilise la vue inline, les modifications apport\xE9es aux mots sont restitu\xE9es inline.","Utilisez les API de la plateforme pour d\xE9tecter lorsqu'un lecteur d'\xE9cran est connect\xE9.","Optimiser pour une utilisation avec un lecteur d'\xE9cran.","Supposons qu\u2019aucun lecteur d\u2019\xE9cran ne soit connect\xE9.","Contr\xF4le si l\u2019interface utilisateur doit s\u2019ex\xE9cuter dans un mode o\xF9 elle est optimis\xE9e pour les lecteurs d\u2019\xE9cran.","Contr\xF4le si un espace est ins\xE9r\xE9 pour les commentaires.","Contr\xF4le si les lignes vides doivent \xEAtre ignor\xE9es avec des actions d'activation/de d\xE9sactivation, d'ajout ou de suppression des commentaires de ligne.","Contr\xF4le si la copie sans s\xE9lection permet de copier la ligne actuelle.","Contr\xF4le si le curseur doit sauter pour rechercher les correspondances lors de la saisie.","Ne lancez jamais la cha\xEEne de recherche dans la s\xE9lection de l\u2019\xE9diteur.","Toujours amorcer la cha\xEEne de recherche \xE0 partir de la s\xE9lection de l\u2019\xE9diteur, y compris le mot \xE0 la position du curseur.","Cha\xEEne de recherche initiale uniquement dans la s\xE9lection de l\u2019\xE9diteur.","D\xE9termine si la cha\xEEne de recherche dans le Widget Recherche est initialis\xE9e avec la s\xE9lection de l\u2019\xE9diteur.","Ne jamais activer automatiquement la recherche dans la s\xE9lection (par d\xE9faut).","Toujours activer automatiquement la recherche dans la s\xE9lection.","Activez Rechercher automatiquement dans la s\xE9lection quand plusieurs lignes de contenu sont s\xE9lectionn\xE9es.","Contr\xF4le la condition d'activation automatique de la recherche dans la s\xE9lection.","D\xE9termine si le Widget Recherche devrait lire ou modifier le presse-papiers de recherche partag\xE9 sur macOS.","Contr\xF4le si le widget Recherche doit ajouter des lignes suppl\xE9mentaires en haut de l'\xE9diteur. Quand la valeur est true, vous pouvez faire d\xE9filer au-del\xE0 de la premi\xE8re ligne si le widget Recherche est visible.","Contr\xF4le si la recherche red\xE9marre automatiquement depuis le d\xE9but (ou la fin) quand il n'existe aucune autre correspondance.","Active/d\xE9sactive les ligatures de police (fonctionnalit\xE9s de police 'calt' et 'liga'). Remplacez ceci par une cha\xEEne pour contr\xF4ler de mani\xE8re pr\xE9cise la propri\xE9t\xE9 CSS 'font-feature-settings'.","Propri\xE9t\xE9 CSS 'font-feature-settings' explicite. Vous pouvez passer une valeur bool\xE9enne \xE0 la place si vous devez uniquement activer/d\xE9sactiver les ligatures.","Configure les ligatures de police ou les fonctionnalit\xE9s de police. Il peut s'agir d'une valeur bool\xE9enne permettant d'activer/de d\xE9sactiver les ligatures, ou d'une cha\xEEne correspondant \xE0 la valeur de la propri\xE9t\xE9 CSS 'font-feature-settings'.","Active/d\xE9sactive la traduction de font-weight en font-variation-settings. Remplacez ce param\xE8tre par une cha\xEEne pour un contr\xF4le affin\xE9 de la propri\xE9t\xE9 CSS 'font-variation-settings'.","Propri\xE9t\xE9 CSS 'font-variation-settings' explicite. Une valeur bool\xE9enne peut \xEAtre pass\xE9e \xE0 la place si une seule valeur doit traduire font-weight en font-variation-settings.","Configure les variations de la police. Il peut s\u2019agir d\u2019une valeur bool\xE9enne pour activer/d\xE9sactiver la traduction de font-weight en font-variation-settings ou d\u2019une cha\xEEne pour la valeur de la propri\xE9t\xE9 CSS 'font-variation-settings'.","Contr\xF4le la taille de police en pixels.",'Seuls les mots cl\xE9s "normal" et "bold", ou les nombres compris entre\xA01 et\xA01\xA0000 sont autoris\xE9s.',`Contr\xF4le l'\xE9paisseur de police. Accepte les mots cl\xE9s "normal" et "bold", ou les nombres compris entre\xA01 et\xA01\xA0000.`,"Montrer l\u2019aper\xE7u des r\xE9sultats (par d\xE9faut)","Acc\xE9der au r\xE9sultat principal et montrer un aper\xE7u","Acc\xE9der au r\xE9sultat principal et activer l\u2019acc\xE8s sans aper\xE7u pour les autres","Ce param\xE8tre est d\xE9pr\xE9ci\xE9, utilisez des param\xE8tres distincts comme 'editor.editor.gotoLocation.multipleDefinitions' ou 'editor.editor.gotoLocation.multipleImplementations' \xE0 la place.","Contr\xF4le le comportement de la commande 'Atteindre la d\xE9finition' quand plusieurs emplacements cibles existent.","Contr\xF4le le comportement de la commande 'Atteindre la d\xE9finition de type' quand plusieurs emplacements cibles existent.","Contr\xF4le le comportement de la commande 'Atteindre la d\xE9claration' quand plusieurs emplacements cibles existent.","Contr\xF4le le comportement de la commande 'Atteindre les impl\xE9mentations' quand plusieurs emplacements cibles existent.","Contr\xF4le le comportement de la commande 'Atteindre les r\xE9f\xE9rences' quand plusieurs emplacements cibles existent.","ID de commande alternatif ex\xE9cut\xE9 quand le r\xE9sultat de 'Atteindre la d\xE9finition' est l'emplacement actuel.","ID de commande alternatif ex\xE9cut\xE9 quand le r\xE9sultat de 'Atteindre la d\xE9finition de type' est l'emplacement actuel.","ID de commande alternatif ex\xE9cut\xE9 quand le r\xE9sultat de 'Atteindre la d\xE9claration' est l'emplacement actuel.","ID de commande alternatif ex\xE9cut\xE9 quand le r\xE9sultat de 'Atteindre l'impl\xE9mentation' est l'emplacement actuel.","ID de commande alternatif ex\xE9cut\xE9 quand le r\xE9sultat de 'Atteindre la r\xE9f\xE9rence' est l'emplacement actuel.","Contr\xF4le si le pointage est affich\xE9.","Contr\xF4le le d\xE9lai en millisecondes, apr\xE8s lequel le survol est affich\xE9.","Contr\xF4le si le pointage doit rester visible quand la souris est d\xE9plac\xE9e au-dessus.","Contr\xF4le le d\xE9lai en millisecondes apr\xE8s lequel le survol est masqu\xE9. N\xE9cessite que \xAB editor.hover.sticky \xBB soit activ\xE9.","Pr\xE9f\xE9rez afficher les points au-dessus de la ligne, s\u2019il y a de l\u2019espace.","Suppose que tous les caract\xE8res ont la m\xEAme largeur. Il s'agit d'un algorithme rapide qui fonctionne correctement pour les polices \xE0 espacement fixe et certains scripts (comme les caract\xE8res latins) o\xF9 les glyphes ont la m\xEAme largeur.","D\xE9l\xE8gue le calcul des points de wrapping au navigateur. Il s'agit d'un algorithme lent qui peut provoquer le gel des grands fichiers, mais qui fonctionne correctement dans tous les cas.","Contr\xF4le l\u2019algorithme qui calcule les points d\u2019habillage. Notez qu\u2019en mode d\u2019accessibilit\xE9, les options avanc\xE9es sont utilis\xE9es pour une exp\xE9rience optimale.","D\xE9sactiver le menu d\u2019action du code.","Afficher le menu d\u2019action du code lorsque le curseur se trouve sur des lignes avec du code.","Afficher le menu d\u2019action du code lorsque le curseur se trouve sur des lignes avec du code ou sur des lignes vides.","Active l\u2019ampoule d\u2019action de code dans l\u2019\xE9diteur.","Affiche les \xE9tendues actives imbriqu\xE9s pendant le d\xE9filement en haut de l\u2019\xE9diteur.","D\xE9finit le nombre maximal de lignes r\xE9manentes \xE0 afficher.","D\xE9finit le mod\xE8le \xE0 utiliser pour d\xE9terminer les lignes \xE0 coller. Si le mod\xE8le hi\xE9rarchique n\u2019existe pas, il revient au mod\xE8le de fournisseur de pliage qui revient au mod\xE8le de mise en retrait. Cette demande est respect\xE9e dans les trois cas.","Activez le d\xE9filement de Sticky Scroll avec la barre de d\xE9filement horizontale de l'\xE9diteur.","Active les indicateurs inlay dans l\u2019\xE9diteur.","Les indicateurs d\u2019inlay sont activ\xE9s.","Les indicateurs d\u2019inlay sont affich\xE9s par d\xE9faut et masqu\xE9s lors de la conservation {0}","Les indicateurs d\u2019inlay sont masqu\xE9s par d\xE9faut et s\u2019affichent lorsque vous maintenez {0}","Les indicateurs d\u2019inlay sont d\xE9sactiv\xE9s.","Contr\xF4le la taille de police des indicateurs d\u2019inlay dans l\u2019\xE9diteur. Par d\xE9faut, le {0} est utilis\xE9 lorsque la valeur configur\xE9e est inf\xE9rieure \xE0 {1} ou sup\xE9rieure \xE0 la taille de police de l\u2019\xE9diteur.","Contr\xF4le la famille de polices des indicateurs d\u2019inlay dans l\u2019\xE9diteur. Lorsqu\u2019il est d\xE9fini sur vide, le {0} est utilis\xE9.","Active le remplissage autour des indicateurs d\u2019inlay dans l\u2019\xE9diteur.",`Contr\xF4le la hauteur de ligne. \r
 - Utilisez 0 pour calculer automatiquement la hauteur de ligne \xE0 partir de la taille de police.\r
 : les valeurs comprises entre 0 et 8 sont utilis\xE9es comme multiplicateur avec la taille de police.\r
 : les valeurs sup\xE9rieures ou \xE9gales \xE0 8 seront utilis\xE9es comme valeurs effectives.`,"Contr\xF4le si la minimap est affich\xE9e.","Contr\xF4le si la minimap est masqu\xE9e automatiquement.","Le minimap a la m\xEAme taille que le contenu de l'\xE9diteur (d\xE9filement possible).","Le minimap s'agrandit ou se r\xE9duit selon les besoins pour remplir la hauteur de l'\xE9diteur (pas de d\xE9filement).","Le minimap est r\xE9duit si n\xE9cessaire pour ne jamais d\xE9passer la taille de l'\xE9diteur (pas de d\xE9filement).","Contr\xF4le la taille du minimap.","Contr\xF4le le c\xF4t\xE9 o\xF9 afficher la minimap.","Contr\xF4le quand afficher le curseur du minimap.","\xC9chelle du contenu dessin\xE9 dans le minimap\xA0: 1, 2\xA0ou\xA03.","Afficher les caract\xE8res r\xE9els sur une ligne par opposition aux blocs de couleur.","Limiter la largeur de la minimap pour afficher au plus un certain nombre de colonnes.","Contr\xF4le si les r\xE9gions nomm\xE9es sont affich\xE9es en tant qu\u2019en-t\xEAtes de section dans la minimap.","Contr\xF4le si les commentaires MARK\xA0: sont affich\xE9s en tant qu\u2019en-t\xEAtes de section dans la minimap.","Contr\xF4le la taille de police des en-t\xEAtes de section dans le minimap.","Contr\xF4le la quantit\xE9 d\u2019espace (en pixels) entre les caract\xE8res de l\u2019en-t\xEAte de section. Cela permet de lire l\u2019en-t\xEAte en petites tailles de police.","Contr\xF4le la quantit\xE9 d\u2019espace entre le bord sup\xE9rieur de l\u2019\xE9diteur et la premi\xE8re ligne.","Contr\xF4le la quantit\xE9 d'espace entre le bord inf\xE9rieur de l'\xE9diteur et la derni\xE8re ligne.","Active une fen\xEAtre contextuelle qui affiche de la documentation sur les param\xE8tres et des informations sur les types \xE0 mesure que vous tapez.","D\xE9termine si le menu de suggestions de param\xE8tres se ferme ou reviens au d\xE9but lorsque la fin de la liste est atteinte.","Des suggestions rapides s\u2019affichent dans le widget de suggestion","Les suggestions rapides s\u2019affichent sous forme de texte fant\xF4me","Les suggestions rapides sont d\xE9sactiv\xE9es","Activez les suggestions rapides dans les cha\xEEnes.","Activez les suggestions rapides dans les commentaires.","Activez les suggestions rapides en dehors des cha\xEEnes et des commentaires.","Contr\xF4le si les suggestions doivent s\u2019afficher automatiquement lors de la saisie. Cela peut \xEAtre contr\xF4l\xE9 pour la saisie dans des commentaires, des cha\xEEnes et d\u2019autres codes. Vous pouvez configurer la suggestion rapide pour qu\u2019elle s\u2019affiche sous forme de texte fant\xF4me ou avec le widget de suggestion. Tenez \xE9galement compte du {0}-setting qui contr\xF4le si les suggestions sont d\xE9clench\xE9es par des caract\xE8res sp\xE9ciaux.","Les num\xE9ros de ligne ne sont pas affich\xE9s.","Les num\xE9ros de ligne sont affich\xE9s en nombre absolu.","Les num\xE9ros de ligne sont affich\xE9s sous la forme de distance en lignes \xE0 la position du curseur.","Les num\xE9ros de ligne sont affich\xE9s toutes les 10 lignes.","Contr\xF4le l'affichage des num\xE9ros de ligne.","Nombre de caract\xE8res monospace auxquels cette r\xE8gle d'\xE9diteur effectue le rendu.","Couleur de cette r\xE8gle d'\xE9diteur.","Rendre les r\xE8gles verticales apr\xE8s un certain nombre de caract\xE8res \xE0 espacement fixe. Utiliser plusieurs valeurs pour plusieurs r\xE8gles. Aucune r\xE8gle n'est dessin\xE9e si le tableau est vide.","La barre de d\xE9filement verticale sera visible uniquement lorsque cela est n\xE9cessaire.","La barre de d\xE9filement verticale est toujours visible.","La barre de d\xE9filement verticale est toujours masqu\xE9e.","Contr\xF4le la visibilit\xE9 de la barre de d\xE9filement verticale.","La barre de d\xE9filement horizontale sera visible uniquement lorsque cela est n\xE9cessaire.","La barre de d\xE9filement horizontale est toujours visible.","La barre de d\xE9filement horizontale est toujours masqu\xE9e.","Contr\xF4le la visibilit\xE9 de la barre de d\xE9filement horizontale.","Largeur de la barre de d\xE9filement verticale.","Hauteur de la barre de d\xE9filement horizontale.","Contr\xF4le si les clics permettent de faire d\xE9filer par page ou d\u2019acc\xE9der \xE0 la position de clic.","Lorsqu'elle est d\xE9finie, la barre de d\xE9filement horizontale n'augmentera pas la taille du contenu de l'\xE9diteur.","Contr\xF4le si tous les caract\xE8res ASCII non basiques sont mis en surbrillance. Seuls les caract\xE8res compris entre U+0020 et U+007E, tabulation, saut de ligne et retour chariot sont consid\xE9r\xE9s comme des ASCII de base.","Contr\xF4le si les caract\xE8res qui r\xE9servent de l\u2019espace ou qui n\u2019ont pas de largeur sont mis en surbrillance.","Contr\xF4le si les caract\xE8res mis en surbrillance peuvent \xEAtre d\xE9concert\xE9s avec des caract\xE8res ASCII de base, \xE0 l\u2019exception de ceux qui sont courants dans les param\xE8tres r\xE9gionaux utilisateur actuels.","Contr\xF4le si les caract\xE8res des commentaires doivent \xE9galement faire l\u2019objet d\u2019une mise en surbrillance Unicode.","Contr\xF4le si les caract\xE8res des cha\xEEnes de texte doivent \xE9galement faire l\u2019objet d\u2019une mise en surbrillance Unicode.","D\xE9finit les caract\xE8res autoris\xE9s qui ne sont pas mis en surbrillance.","Les caract\xE8res Unicode communs aux param\xE8tres r\xE9gionaux autoris\xE9s ne sont pas mis en surbrillance.","Contr\xF4le si les suggestions en ligne doivent \xEAtre affich\xE9es automatiquement dans l\u2019\xE9diteur.","Afficher la barre d\u2019outils de suggestion en ligne chaque fois qu\u2019une suggestion inline est affich\xE9e.","Afficher la barre d\u2019outils de suggestion en ligne lorsque vous pointez sur une suggestion incluse.","N\u2019affichez jamais la barre d\u2019outils de suggestion en ligne.","Contr\xF4le quand afficher la barre d\u2019outils de suggestion incluse.","Contr\xF4le la fa\xE7on dont les suggestions inline interagissent avec le widget de suggestion. Si cette option est activ\xE9e, le widget de suggestion n\u2019est pas affich\xE9 automatiquement lorsque des suggestions inline sont disponibles.","Contr\xF4le la famille de polices des suggestions inlined.",null,null,null,null,null,null,"Contr\xF4le si la colorisation des paires de crochets est activ\xE9e ou non. Utilisez {0} pour remplacer les couleurs de surbrillance des crochets.","Contr\xF4le si chaque type de crochet poss\xE8de son propre pool de couleurs ind\xE9pendant.","D\xE9sactive les rep\xE8res de paire de crochets.","Active les rep\xE8res de paire de crochets uniquement pour la paire de crochets actifs.","D\xE9sactive les rep\xE8res de paire de crochets.","Contr\xF4le si les guides de la paire de crochets sont activ\xE9s ou non.","Active les rep\xE8res horizontaux en plus des rep\xE8res de paire de crochets verticaux.","Active les rep\xE8res horizontaux uniquement pour la paire de crochets actifs.","D\xE9sactive les rep\xE8res de paire de crochets horizontaux.","Contr\xF4le si les guides de la paire de crochets horizontaux sont activ\xE9s ou non.","Contr\xF4le si l\u2019\xE9diteur doit mettre en surbrillance la paire de crochets actifs.","Contr\xF4le si l\u2019\xE9diteur doit afficher les guides de mise en retrait.","Met en surbrillance le guide de retrait actif.","Met en surbrillance le rep\xE8re de retrait actif m\xEAme si les rep\xE8res de crochet sont mis en surbrillance.","Ne mettez pas en surbrillance le rep\xE8re de retrait actif.","Contr\xF4le si l\u2019\xE9diteur doit mettre en surbrillance le guide de mise en retrait actif.","Ins\xE9rez une suggestion sans remplacer le texte \xE0 droite du curseur.","Ins\xE9rez une suggestion et remplacez le texte \xE0 droite du curseur.","Contr\xF4le si les mots sont remplac\xE9s en cas d'acceptation de la saisie semi-automatique. Notez que cela d\xE9pend des extensions adh\xE9rant \xE0 cette fonctionnalit\xE9.","D\xE9termine si le filtre et le tri des suggestions doivent prendre en compte les fautes de frappes mineures.","Contr\xF4le si le tri favorise les mots qui apparaissent \xE0 proximit\xE9 du curseur.","Contr\xF4le si les s\xE9lections de suggestion m\xE9moris\xE9es sont partag\xE9es entre plusieurs espaces de travail et fen\xEAtres (n\xE9cessite '#editor.suggestSelection#').","Toujours s\xE9lectionner une suggestion lors du d\xE9clenchement automatique d\u2019IntelliSense.","Ne jamais s\xE9lectionner une suggestion lors du d\xE9clenchement automatique d\u2019IntelliSense.","S\xE9lectionnez une suggestion uniquement lors du d\xE9clenchement d\u2019IntelliSense \xE0 partir d\u2019un caract\xE8re d\xE9clencheur.","S\xE9lectionnez une suggestion uniquement lors du d\xE9clenchement d\u2019IntelliSense au cours de la frappe.","Contr\xF4le si une suggestion est s\xE9lectionn\xE9e lorsque le widget s\u2019affiche. Notez que cela ne s\u2019applique qu\u2019aux suggestions d\xE9clench\xE9es automatiquement ({0} et {1}) et qu\u2019une suggestion est toujours s\xE9lectionn\xE9e lorsqu\u2019elle est explicitement invoqu\xE9e, par exemple via `Ctrl+Espace`.","Contr\xF4le si un extrait de code actif emp\xEAche les suggestions rapides.","Contr\xF4le s'il faut montrer ou masquer les ic\xF4nes dans les suggestions.","Contr\xF4le la visibilit\xE9 de la barre d'\xE9tat en bas du widget de suggestion.","Contr\xF4le si la sortie de la suggestion doit \xEAtre affich\xE9e en aper\xE7u dans l\u2019\xE9diteur.","D\xE9termine si les d\xE9tails du widget de suggestion sont inclus dans l\u2019\xE9tiquette ou uniquement dans le widget de d\xE9tails.","Ce param\xE8tre est d\xE9pr\xE9ci\xE9. Le widget de suggestion peut d\xE9sormais \xEAtre redimensionn\xE9.","Ce param\xE8tre est d\xE9pr\xE9ci\xE9, veuillez utiliser des param\xE8tres distincts comme 'editor.suggest.showKeywords' ou 'editor.suggest.showSnippets' \xE0 la place.","Si activ\xE9, IntelliSense montre des suggestions de type 'method'.","Si activ\xE9, IntelliSense montre des suggestions de type 'function'.","Si activ\xE9, IntelliSense montre des suggestions de type 'constructor'.","Si cette option est activ\xE9e, IntelliSense montre des suggestions `d\xE9pr\xE9ci\xE9es`.","Quand le filtrage IntelliSense est activ\xE9, le premier caract\xE8re correspond \xE0 un d\xE9but de mot, par exemple 'c' sur 'Console' ou 'WebContext', mais _not_ sur 'description'. Si d\xE9sactiv\xE9, IntelliSense affiche plus de r\xE9sultats, mais les trie toujours par qualit\xE9 de correspondance.","Si activ\xE9, IntelliSense montre des suggestions de type 'field'.","Si activ\xE9, IntelliSense montre des suggestions de type 'variable'.","Si activ\xE9, IntelliSense montre des suggestions de type 'class'.","Si activ\xE9, IntelliSense montre des suggestions de type 'struct'.","Si activ\xE9, IntelliSense montre des suggestions de type 'interface'.","Si activ\xE9, IntelliSense montre des suggestions de type 'module'.","Si activ\xE9, IntelliSense montre des suggestions de type 'property'.","Si activ\xE9, IntelliSense montre des suggestions de type 'event'.","Si activ\xE9, IntelliSense montre des suggestions de type 'operator'.","Si activ\xE9, IntelliSense montre des suggestions de type 'unit'.","Si activ\xE9, IntelliSense montre des suggestions de type 'value'.","Si activ\xE9, IntelliSense montre des suggestions de type 'constant'.","Si activ\xE9, IntelliSense montre des suggestions de type 'enum'.","Si activ\xE9, IntelliSense montre des suggestions de type 'enumMember'.","Si activ\xE9, IntelliSense montre des suggestions de type 'keyword'.","Si activ\xE9, IntelliSense montre des suggestions de type 'text'.","Si activ\xE9, IntelliSense montre des suggestions de type 'color'.","Si activ\xE9, IntelliSense montre des suggestions de type 'file'.","Si activ\xE9, IntelliSense montre des suggestions de type 'reference'.","Si activ\xE9, IntelliSense montre des suggestions de type 'customcolor'.","Si activ\xE9, IntelliSense montre des suggestions de type 'folder'.","Si activ\xE9, IntelliSense montre des suggestions de type 'typeParameter'.","Si activ\xE9, IntelliSense montre des suggestions de type 'snippet'.","Si activ\xE9, IntelliSense montre des suggestions de type 'utilisateur'.","Si activ\xE9, IntelliSense montre des suggestions de type 'probl\xE8mes'.","Indique si les espaces blancs de d\xE9but et de fin doivent toujours \xEAtre s\xE9lectionn\xE9s.","Indique si les sous-mots (tels que \xAB foo \xBB dans \xAB fooBar \xBB ou \xAB foo_bar \xBB) doivent \xEAtre s\xE9lectionn\xE9s.","Param\xE8tres r\xE9gionaux \xE0 utiliser pour la segmentation de mots lors de navigations ou d\u2019op\xE9rations li\xE9es \xE0 un mot. Sp\xE9cifiez la balise de langue BCP 47 du mot que vous souhaitez reconna\xEEtre (par exemple, ja, zh-CN, zh-Hant-TW, etc.).","Param\xE8tres r\xE9gionaux \xE0 utiliser pour la segmentation de mots lors de navigations ou d\u2019op\xE9rations li\xE9es \xE0 un mot. Sp\xE9cifiez la balise de langue BCP 47 du mot que vous souhaitez reconna\xEEtre (par exemple, ja, zh-CN, zh-Hant-TW, etc.).","Aucune mise en retrait. Les lignes envelopp\xE9es commencent \xE0 la colonne 1.","Les lignes envelopp\xE9es obtiennent la m\xEAme mise en retrait que le parent.","Les lignes justifi\xE9es obtiennent une mise en retrait +1 vers le parent.","Les lignes justifi\xE9es obtiennent une mise en retrait +2 vers le parent. ","Contr\xF4le la mise en retrait des lignes justifi\xE9es.","Contr\xF4le si vous pouvez glisser et d\xE9poser un fichier dans un \xE9diteur de texte en maintenant la touche \xAB\xA0Maj\xA0\xBB enfonc\xE9e (au lieu d\u2019ouvrir le fichier dans un \xE9diteur).","Contr\xF4le si un widget est affich\xE9 lors de l\u2019annulation de fichiers dans l\u2019\xE9diteur. Ce widget vous permet de contr\xF4ler la fa\xE7on dont le fichier est annul\xE9.","Afficher le widget du s\xE9lecteur de d\xE9p\xF4t apr\xE8s la suppression d\u2019un fichier dans l\u2019\xE9diteur.","Ne jamais afficher le widget du s\xE9lecteur de d\xE9p\xF4t. \xC0 la place, le fournisseur de d\xE9p\xF4t par d\xE9faut est toujours utilis\xE9.","Contr\xF4le si vous pouvez coller le contenu de diff\xE9rentes mani\xE8res.","Contr\xF4le l\u2019affichage d\u2019un widget lors du collage de contenu dans l\u2019\xE9diteur. Ce widget vous permet de contr\xF4ler la mani\xE8re dont le fichier est coll\xE9.","Afficher le widget du s\xE9lecteur de collage une fois le contenu coll\xE9 dans l\u2019\xE9diteur.","Ne jamais afficher le widget de s\xE9lection de collage. Au lieu de cela, le comportement de collage par d\xE9faut est toujours utilis\xE9.","Contr\xF4le si les suggestions doivent \xEAtre accept\xE9es sur les caract\xE8res de validation. Par exemple, en JavaScript, le point-virgule (`;`) peut \xEAtre un caract\xE8re de validation qui accepte une suggestion et tape ce caract\xE8re.","Accepter uniquement une suggestion avec 'Entr\xE9e' quand elle effectue une modification textuelle.","Contr\xF4le si les suggestions sont accept\xE9es apr\xE8s appui sur 'Entr\xE9e', en plus de 'Tab'. Permet d\u2019\xE9viter toute ambigu\xEFt\xE9 entre l\u2019insertion de nouvelles lignes et l'acceptation de suggestions.","Contr\xF4le le nombre de lignes de l\u2019\xE9diteur qu\u2019un lecteur d\u2019\xE9cran peut lire en une seule fois. Quand nous d\xE9tectons un lecteur d\u2019\xE9cran, nous d\xE9finissons automatiquement la valeur par d\xE9faut \xE0 500. Attention\xA0: Les valeurs sup\xE9rieures \xE0 la valeur par d\xE9faut peuvent avoir un impact important sur les performances.","Contenu de l'\xE9diteur","Contr\xF4lez si les suggestions incluses sont annonc\xE9es par un lecteur d\u2019\xE9cran.","Utilisez les configurations de langage pour d\xE9terminer quand fermer automatiquement les parenth\xE8ses.","Fermer automatiquement les parenth\xE8ses uniquement lorsque le curseur est \xE0 gauche de l\u2019espace.","Contr\xF4le si l\u2019\xE9diteur doit fermer automatiquement les parenth\xE8ses quand l\u2019utilisateur ajoute une parenth\xE8se ouvrante.","Utilisez les configurations de langage pour d\xE9terminer quand fermer automatiquement les commentaires.","Fermez automatiquement les commentaires seulement si le curseur est \xE0 gauche de l'espace.","Contr\xF4le si l'\xE9diteur doit fermer automatiquement les commentaires quand l'utilisateur ajoute un commentaire ouvrant.","Supprimez les guillemets ou crochets fermants adjacents uniquement s'ils ont \xE9t\xE9 ins\xE9r\xE9s automatiquement.","Contr\xF4le si l'\xE9diteur doit supprimer les guillemets ou crochets fermants adjacents au moment de la suppression.","Tapez avant les guillemets ou les crochets fermants uniquement s'ils sont automatiquement ins\xE9r\xE9s.","Contr\xF4le si l'\xE9diteur doit taper avant les guillemets ou crochets fermants.","Utilisez les configurations de langage pour d\xE9terminer quand fermer automatiquement les guillemets.","Fermer automatiquement les guillemets uniquement lorsque le curseur est \xE0 gauche de l\u2019espace.","Contr\xF4le si l\u2019\xE9diteur doit fermer automatiquement les guillemets apr\xE8s que l\u2019utilisateur ajoute un guillemet ouvrant.","L'\xE9diteur n'ins\xE8re pas de retrait automatiquement.","L'\xE9diteur conserve le retrait de la ligne actuelle.","L'\xE9diteur conserve le retrait de la ligne actuelle et honore les crochets d\xE9finis par le langage.","L'\xE9diteur conserve le retrait de la ligne actuelle, honore les crochets d\xE9finis par le langage et appelle des objets onEnterRules sp\xE9ciaux d\xE9finis par les langages.","L'\xE9diteur conserve le retrait de la ligne actuelle, honore les crochets d\xE9finis par le langage, appelle des objets onEnterRules sp\xE9ciaux d\xE9finis par les langages et honore les objets indentationRules d\xE9finis par les langages.","Contr\xF4le si l'\xE9diteur doit ajuster automatiquement le retrait quand les utilisateurs tapent, collent, d\xE9placent ou mettent en retrait des lignes.","Utilisez les configurations de langage pour d\xE9terminer quand entourer automatiquement les s\xE9lections.","Entourez avec des guillemets et non des crochets.","Entourez avec des crochets et non des guillemets.","Contr\xF4le si l'\xE9diteur doit automatiquement entourer les s\xE9lections quand l'utilisateur tape des guillemets ou des crochets.","\xC9mule le comportement des tabulations pour la s\xE9lection quand des espaces sont utilis\xE9s \xE0 des fins de mise en retrait. La s\xE9lection respecte les taquets de tabulation.","Contr\xF4le si l'\xE9diteur affiche CodeLens.","Contr\xF4le la famille de polices pour CodeLens.","Contr\xF4le la taille de police en pixels pour CodeLens. Quand la valeur est 0, 90\xA0% de '#editor.fontSize#' est utilis\xE9.","Contr\xF4le si l'\xE9diteur doit afficher les \xE9l\xE9ments d\xE9coratifs de couleurs inline et le s\xE9lecteur de couleurs.","Faire appara\xEEtre le s\xE9lecteur de couleurs au clic et au pointage de l\u2019\xE9l\xE9ment d\xE9coratif de couleurs","Faire appara\xEEtre le s\xE9lecteur de couleurs en survolant l\u2019\xE9l\xE9ment d\xE9coratif de couleurs","Faire appara\xEEtre le s\xE9lecteur de couleurs en cliquant sur l\u2019\xE9l\xE9ment d\xE9coratif de couleurs","Contr\xF4le la condition pour faire appara\xEEtre un s\xE9lecteur de couleurs \xE0 partir d\u2019un \xE9l\xE9ment d\xE9coratif de couleurs","Contr\xF4le le nombre maximal d\u2019\xE9l\xE9ments d\xE9coratifs de couleur qui peuvent \xEAtre rendus simultan\xE9ment dans un \xE9diteur.","Autoriser l'utilisation de la souris et des touches pour s\xE9lectionner des colonnes.","Contr\xF4le si la coloration syntaxique doit \xEAtre copi\xE9e dans le presse-papiers.","Contr\xF4ler le style d\u2019animation du curseur.","L\u2019animation de caret fluide est d\xE9sactiv\xE9e.","L\u2019animation de caret fluide est activ\xE9e uniquement lorsque l\u2019utilisateur d\xE9place le curseur avec un mouvement explicite.","L\u2019animation de caret fluide est toujours activ\xE9e.","Contr\xF4le si l'animation du point d'insertion doit \xEAtre activ\xE9e.","Contr\xF4le le style du curseur en mode d\u2019entr\xE9e d\u2019insertion.","Contr\xF4le le nombre minimal de lignes de d\xE9but (0 minimum) et de fin (1 minimum) visibles autour du curseur. \xC9galement appel\xE9 \xAB\xA0scrollOff\xA0\xBB ou \xAB\xA0scrollOffset\xA0\xBB dans d'autres \xE9diteurs.","'cursorSurroundingLines' est appliqu\xE9 seulement s'il est d\xE9clench\xE9 via le clavier ou une API.","'cursorSurroundingLines' est toujours appliqu\xE9.","Contr\xF4le le moment o\xF9 #editor.cursorSurroundingLines# doit \xEAtre appliqu\xE9.","D\xE9termine la largeur du curseur lorsque `#editor.cursorStyle#` est \xE0 `line`.","Contr\xF4le si l\u2019\xE9diteur autorise le d\xE9placement de s\xE9lections par glisser-d\xE9placer.","Utilisez une nouvelle m\xE9thode de rendu avec des SVG.","Utilisez une nouvelle m\xE9thode de rendu avec des caract\xE8res de police.","Utilisez la m\xE9thode de rendu stable.","Contr\xF4le si les espaces blancs sont rendus avec une nouvelle m\xE9thode exp\xE9rimentale.","Multiplicateur de vitesse de d\xE9filement quand vous appuyez sur 'Alt'.","Contr\xF4le si l'\xE9diteur a le pliage de code activ\xE9.","Utilisez une strat\xE9gie de pliage propre au langage, si disponible, sinon utilisez la strat\xE9gie bas\xE9e sur le retrait.","Utilisez la strat\xE9gie de pliage bas\xE9e sur le retrait.","Contr\xF4le la strat\xE9gie de calcul des plages de pliage.","Contr\xF4le si l'\xE9diteur doit mettre en \xE9vidence les plages pli\xE9es.","Contr\xF4le si l\u2019\xE9diteur r\xE9duit automatiquement les plages d\u2019importation.","Nombre maximal de r\xE9gions pliables. L\u2019augmentation de cette valeur peut r\xE9duire la r\xE9activit\xE9 de l\u2019\xE9diteur lorsque la source actuelle comprend un grand nombre de r\xE9gions pliables.","Contr\xF4le si le fait de cliquer sur le contenu vide apr\xE8s une ligne pli\xE9e d\xE9plie la ligne.","Contr\xF4le la famille de polices.","D\xE9termine si l\u2019\xE9diteur doit automatiquement mettre en forme le contenu coll\xE9. Un formateur doit \xEAtre disponible et \xEAtre capable de mettre en forme une plage dans un document.","Contr\xF4le si l\u2019\xE9diteur doit mettre automatiquement en forme la ligne apr\xE8s la saisie.","Contr\xF4le si l'\xE9diteur doit afficher la marge de glyphes verticale. La marge de glyphes sert principalement au d\xE9bogage.","Contr\xF4le si le curseur doit \xEAtre masqu\xE9 dans la r\xE8gle de la vue d\u2019ensemble.","Contr\xF4le l'espacement des lettres en pixels.","Contr\xF4le si la modification li\xE9e est activ\xE9e dans l\u2019\xE9diteur. En fonction du langage, les symboles associ\xE9s, par exemple les balises HTML, sont mis \xE0 jour durant le processus de modification.","Contr\xF4le si l\u2019\xE9diteur doit d\xE9tecter les liens et les rendre cliquables.","Mettez en surbrillance les crochets correspondants.","Un multiplicateur \xE0 utiliser sur les `deltaX` et `deltaY` des \xE9v\xE9nements de d\xE9filement de roulette de souris.","Faites un zoom sur la police de l\u2019\xE9diteur quand l\u2019utilisateur fait tourner la roulette de la souris tout en maintenant la touche \xAB\xA0Cmd\xA0\xBB enfonc\xE9e.","Faire un zoom sur la police de l'\xE9diteur quand l'utilisateur fait tourner la roulette de la souris tout en maintenant la touche 'Ctrl' enfonc\xE9e.","Fusionnez plusieurs curseurs quand ils se chevauchent.","Mappe vers 'Contr\xF4le' dans Windows et Linux, et vers 'Commande' dans macOS.","Mappe vers 'Alt' dans Windows et Linux, et vers 'Option' dans macOS.","Modificateur \xE0 utiliser pour ajouter plusieurs curseurs avec la souris. Les mouvements de la souris Atteindre la d\xE9finition et Ouvrir le lien s\u2019adaptent afin qu\u2019ils ne soient pas en conflit avec le [modificateur multicurseur](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modificateur).","Chaque curseur colle une seule ligne de texte.","Chaque curseur colle le texte en entier.","Contr\xF4le le collage quand le nombre de lignes du texte coll\xE9 correspond au nombre de curseurs.","Contr\xF4le le nombre maximal de curseurs pouvant se trouver dans un \xE9diteur actif \xE0 la fois.","Ne met pas en surbrillance les occurrences.","Met en surbrillance les occurrences uniquement dans le fichier actif.","Exp\xE9rimental : met en \xE9vidence les occurrences dans tous les fichiers ouverts valides.","Contr\xF4le si les occurrences doivent \xEAtre mises en \xE9vidence dans les fichiers ouverts.","Contr\xF4le si une bordure doit \xEAtre dessin\xE9e autour de la r\xE8gle de la vue d'ensemble.","Focus sur l'arborescence \xE0 l'ouverture de l'aper\xE7u","Placer le focus sur l'\xE9diteur \xE0 l'ouverture de l'aper\xE7u","Contr\xF4le s'il faut mettre le focus sur l'\xE9diteur inline ou sur l'arborescence dans le widget d'aper\xE7u.","Contr\xF4le si le geste de souris Acc\xE9der \xE0 la d\xE9finition ouvre toujours le widget d'aper\xE7u.","Contr\xF4le le d\xE9lai en millisecondes apr\xE8s lequel des suggestions rapides sont affich\xE9es.","Contr\xF4le si l'\xE9diteur renomme automatiquement selon le type.","D\xE9pr\xE9ci\xE9. Utilisez 'editor.linkedEditing' \xE0 la place.","Contr\xF4le si l\u2019\xE9diteur doit afficher les caract\xE8res de contr\xF4le.","Affichez le dernier num\xE9ro de ligne quand le fichier se termine par un saut de ligne.","Met en surbrillance la goutti\xE8re et la ligne actuelle.","Contr\xF4le la fa\xE7on dont l\u2019\xE9diteur doit afficher la mise en surbrillance de la ligne actuelle.","Contr\xF4le si l'\xE9diteur doit afficher la mise en surbrillance de la ligne actuelle uniquement quand il a le focus.","Affiche les espaces blancs \xE0 l'exception des espaces uniques entre les mots.","Afficher les espaces blancs uniquement sur le texte s\xE9lectionn\xE9.","Affiche uniquement les caract\xE8res correspondant aux espaces blancs de fin.","Contr\xF4le la fa\xE7on dont l\u2019\xE9diteur doit restituer les caract\xE8res espaces.","Contr\xF4le si les s\xE9lections doivent avoir des angles arrondis.","Contr\xF4le le nombre de caract\xE8res suppl\xE9mentaires, au-del\xE0 duquel l\u2019\xE9diteur d\xE9file horizontalement.","Contr\xF4le si l\u2019\xE9diteur d\xE9file au-del\xE0 de la derni\xE8re ligne.","Faites d\xE9filer uniquement le long de l'axe pr\xE9dominant quand le d\xE9filement est \xE0 la fois vertical et horizontal. Emp\xEAche la d\xE9rive horizontale en cas de d\xE9filement vertical sur un pav\xE9 tactile.","Contr\xF4le si le presse-papiers principal Linux doit \xEAtre pris en charge.","Contr\xF4le si l'\xE9diteur doit mettre en surbrillance les correspondances similaires \xE0 la s\xE9lection.","Affichez toujours les contr\xF4les de pliage.","N\u2019affichez jamais les contr\xF4les de pliage et r\xE9duisez la taille de la marge.","Affichez uniquement les contr\xF4les de pliage quand la souris est au-dessus de la reliure.","Contr\xF4le quand afficher les contr\xF4les de pliage sur la reliure.","Contr\xF4le la disparition du code inutile.","Contr\xF4le les variables d\xE9pr\xE9ci\xE9es barr\xE9es.","Afficher des suggestions d\u2019extraits au-dessus d\u2019autres suggestions.","Afficher des suggestions d\u2019extraits en-dessous d\u2019autres suggestions.","Afficher des suggestions d\u2019extraits avec d\u2019autres suggestions.","Ne pas afficher de suggestions d\u2019extrait de code.","Contr\xF4le si les extraits de code s'affichent en m\xEAme temps que d'autres suggestions, ainsi que leur mode de tri.","Contr\xF4le si l'\xE9diteur d\xE9file en utilisant une animation.","Contr\xF4le si l'indicateur d'accessibilit\xE9 doit \xEAtre fourni aux utilisateurs du lecteur d'\xE9cran lorsqu'une compl\xE9tion inline est affich\xE9e.","Taille de police pour le widget suggest. Lorsqu\u2019elle est d\xE9finie sur {0}, la valeur de {1} est utilis\xE9e.","Hauteur de ligne pour le widget suggest. Lorsqu\u2019elle est d\xE9finie sur {0}, la valeur de {1} est utilis\xE9e. La valeur minimale est 8.","Contr\xF4le si les suggestions devraient automatiquement s\u2019afficher lorsque vous tapez les caract\xE8res de d\xE9clencheur.","S\xE9lectionnez toujours la premi\xE8re suggestion.","S\xE9lectionnez les suggestions r\xE9centes sauf si une entr\xE9e ult\xE9rieure en a s\xE9lectionn\xE9 une, par ex., 'console.| -> console.log', car 'log' a \xE9t\xE9 effectu\xE9 r\xE9cemment.","S\xE9lectionnez des suggestions en fonction des pr\xE9fixes pr\xE9c\xE9dents qui ont compl\xE9t\xE9 ces suggestions, par ex., 'co -> console' et 'con -> const'.","Contr\xF4le comment les suggestions sont pr\xE9-s\xE9lectionn\xE9s lors de l\u2019affichage de la liste de suggestion.","La compl\xE9tion par tabulation ins\xE9rera la meilleure suggestion lorsque vous appuyez sur tab.","D\xE9sactiver les compl\xE9tions par tabulation.","Compl\xE9ter les extraits de code par tabulation lorsque leur pr\xE9fixe correspond. Fonctionne mieux quand les 'quickSuggestions' ne sont pas activ\xE9es.","Active les compl\xE9tions par tabulation","Les marques de fin de ligne inhabituelles sont automatiquement supprim\xE9es.","Les marques de fin de ligne inhabituelles sont ignor\xE9es.","Les marques de fin de ligne inhabituelles demandent \xE0 \xEAtre supprim\xE9es.","Supprimez les marques de fin de ligne inhabituelles susceptibles de causer des probl\xE8mes.","Les espaces et les onglets sont ins\xE9r\xE9s et supprim\xE9s en alignement avec les taquets de tabulation.","Utilisez la r\xE8gle de saut de ligne par d\xE9faut.","Les sauts de mots ne doivent pas \xEAtre utilis\xE9s pour le texte chinois/japonais/cor\xE9en (CJC). Le comportement du texte non CJC est identique \xE0 celui du texte normal.","Contr\xF4le les r\xE8gles de s\xE9parateur de mots utilis\xE9es pour le texte chinois/japonais/cor\xE9en (CJC).","Caract\xE8res utilis\xE9s comme s\xE9parateurs de mots durant la navigation ou les op\xE9rations bas\xE9es sur les mots","Le retour automatique \xE0 la ligne n'est jamais effectu\xE9.","Le retour automatique \xE0 la ligne s'effectue en fonction de la largeur de la fen\xEAtre d'affichage.","Les lignes seront termin\xE9es \xE0 `#editor.wordWrapColumn#`.","Les lignes seront termin\xE9es au minimum du viewport et `#editor.wordWrapColumn#`.","Contr\xF4le comment les lignes doivent \xEAtre limit\xE9es.","Contr\xF4le la colonne de terminaison de l\u2019\xE9diteur lorsque `#editor.wordWrap#` est \xE0 `wordWrapColumn` ou `bounded`.","Contr\xF4le si les d\xE9corations de couleur inline doivent \xEAtre affich\xE9es \xE0 l\u2019aide du fournisseur de couleurs de document par d\xE9faut","Contr\xF4le si l\u2019\xE9diteur re\xE7oit des onglets ou les reporte au banc d\u2019essai pour la navigation.","Couleur d'arri\xE8re-plan de la mise en surbrillance de la ligne \xE0 la position du curseur.","Couleur d'arri\xE8re-plan de la bordure autour de la ligne \xE0 la position du curseur.","Couleur d'arri\xE8re-plan des plages mises en surbrillance, comme par les fonctionnalit\xE9s de recherche et Quick Open. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arri\xE8re-plan de la bordure autour des plages mises en surbrillance.","Couleur d'arri\xE8re-plan du symbole mis en surbrillance, comme le symbole Atteindre la d\xE9finition ou Suivant/Pr\xE9c\xE9dent. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les d\xE9corations sous-jacentes.","Couleur d'arri\xE8re-plan de la bordure autour des symboles mis en surbrillance.","Couleur du curseur de l'\xE9diteur.","La couleur de fond du curseur de l'\xE9diteur. Permet de personnaliser la couleur d'un caract\xE8re survol\xE9 par un curseur de bloc.","Couleur du curseur de l\u2019\xE9diteur principal lorsque plusieurs curseurs sont pr\xE9sents.","Couleur d\u2019arri\xE8re-plan du curseur de l\u2019\xE9diteur principal lorsque plusieurs curseurs sont pr\xE9sents. Permet de personnaliser la couleur d'un caract\xE8re survol\xE9 par un curseur de bloc.","Couleur des curseurs de l\u2019\xE9diteur secondaire lorsque plusieurs curseurs sont pr\xE9sents.","Couleur d\u2019arri\xE8re-plan des curseurs de l\u2019\xE9diteur secondaire lorsque plusieurs curseurs sont pr\xE9sents. Permet de personnaliser la couleur d'un caract\xE8re survol\xE9 par un curseur de bloc.","Couleur des espaces blancs dans l'\xE9diteur.","Couleur des num\xE9ros de ligne de l'\xE9diteur.","Couleur des rep\xE8res de retrait de l'\xE9diteur.","'editorIndentGuide.background' est d\xE9conseill\xE9. Utilisez 'editorIndentGuide.background1' \xE0 la place.","Couleur des guides d'indentation de l'\xE9diteur actif","'editorIndentGuide.activeBackground' est d\xE9conseill\xE9. Utilisez 'editorIndentGuide.activeBackground1' \xE0 la place.","Couleur des rep\xE8res de retrait de l'\xE9diteur (1).","Couleur des rep\xE8res de retrait de l'\xE9diteur (2).","Couleur des rep\xE8res de retrait de l'\xE9diteur (3).","Couleur des rep\xE8res de retrait de l'\xE9diteur (4).","Couleur des rep\xE8res de retrait de l'\xE9diteur (5).","Couleur des rep\xE8res de retrait de l'\xE9diteur (6).","Couleur des repaires de retrait de l'\xE9diteur actifs (1).","Couleur des repaires de retrait de l'\xE9diteur actifs (2).","Couleur des repaires de retrait de l'\xE9diteur actifs (3).","Couleur des repaires de retrait de l'\xE9diteur actifs (4).","Couleur des repaires de retrait de l'\xE9diteur actifs (5).","Couleur des repaires de retrait de l'\xE9diteur actifs (6).","Couleur des num\xE9ros de lignes actives de l'\xE9diteur","L\u2019ID est d\xE9pr\xE9ci\xE9. Utilisez \xE0 la place 'editorLineNumber.activeForeground'.","Couleur des num\xE9ros de lignes actives de l'\xE9diteur","Couleur de la ligne finale de l\u2019\xE9diteur lorsque editor.renderFinalNewline est d\xE9fini sur gris\xE9.","Couleur des r\xE8gles de l'\xE9diteur","Couleur pour les indicateurs CodeLens","Couleur d'arri\xE8re-plan pour les accolades associ\xE9es","Couleur pour le contour des accolades associ\xE9es","Couleur de la bordure de la r\xE8gle d'aper\xE7u.","Couleur d\u2019arri\xE8re-plan de la r\xE8gle de vue d\u2019ensemble de l\u2019\xE9diteur.","Couleur de fond pour la bordure de l'\xE9diteur. La bordure contient les marges pour les symboles et les num\xE9ros de ligne.","Couleur de bordure du code source inutile (non utilis\xE9) dans l'\xE9diteur.","Opacit\xE9 du code source inutile (non utilis\xE9) dans l'\xE9diteur. Par exemple, '#000000c0' affiche le code avec une opacit\xE9 de 75\xA0%. Pour les th\xE8mes \xE0 fort contraste, utilisez la couleur de th\xE8me 'editorUnnecessaryCode.border' pour souligner le code inutile au lieu d'utiliser la transparence.","Couleur de bordure du texte fant\xF4me dans l\u2019\xE9diteur.","Couleur de premier plan du texte fant\xF4me dans l\u2019\xE9diteur.","Couleur de l\u2019arri\xE8re-plan du texte fant\xF4me dans l\u2019\xE9diteur","Couleur de marqueur de la r\xE8gle d'aper\xE7u pour la mise en surbrillance des plages. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur du marqueur de la r\xE8gle d'aper\xE7u pour les erreurs.","Couleur du marqueur de la r\xE8gle d'aper\xE7u pour les avertissements.","Couleur du marqueur de la r\xE8gle d'aper\xE7u pour les informations.","Couleur de premier plan des crochets (1). N\xE9cessite l\u2019activation de la coloration de la paire de crochets.","Couleur de premier plan des crochets (2). N\xE9cessite l\u2019activation de la coloration de la paire de crochets.","Couleur de premier plan des crochets (3). N\xE9cessite l\u2019activation de la coloration de la paire de crochets.","Couleur de premier plan des crochets (4). N\xE9cessite l\u2019activation de la coloration de la paire de crochets.","Couleur de premier plan des crochets (5). N\xE9cessite l\u2019activation de la coloration de la paire de crochets.","Couleur de premier plan des crochets (6). N\xE9cessite l\u2019activation de la coloration de la paire de crochets.","Couleur de premier plan des parenth\xE8ses inattendues","Couleur d\u2019arri\xE8re-plan des rep\xE8res de paire de crochets inactifs (1). N\xE9cessite l\u2019activation des rep\xE8res de paire de crochets.","Couleur d\u2019arri\xE8re-plan des rep\xE8res de paire de crochets inactifs (2). N\xE9cessite l\u2019activation des rep\xE8res de paire de crochets.","Couleur d\u2019arri\xE8re-plan des rep\xE8res de paire de crochets inactifs (3). N\xE9cessite l\u2019activation des rep\xE8res de paire de crochets.","Couleur d\u2019arri\xE8re-plan des rep\xE8res de paire de crochets inactifs (4). N\xE9cessite l\u2019activation des rep\xE8res de paire de crochets.","Couleur d\u2019arri\xE8re-plan des rep\xE8res de paire de crochets inactifs (5). N\xE9cessite l\u2019activation des rep\xE8res de paire de crochets.","Couleur d\u2019arri\xE8re-plan des rep\xE8res de paire de crochets inactifs (6). N\xE9cessite l\u2019activation des rep\xE8res de paire de crochets.","Couleur d\u2019arri\xE8re-plan des rep\xE8res de paire de crochets actifs (1). N\xE9cessite l\u2019activation des rep\xE8res de paire de crochets.","Couleur d\u2019arri\xE8re-plan des rep\xE8res de paire de crochets actifs (2). N\xE9cessite l\u2019activation des rep\xE8res de paire de crochets.","Couleur d\u2019arri\xE8re-plan des rep\xE8res de paire de crochets actifs (3). N\xE9cessite l\u2019activation des rep\xE8res de paire de crochets.","Couleur d\u2019arri\xE8re-plan des rep\xE8res de paire de crochets actifs (4). N\xE9cessite l\u2019activation des rep\xE8res de paire de crochets.","Couleur d\u2019arri\xE8re-plan des rep\xE8res de paire de crochets actifs (5). N\xE9cessite l\u2019activation des rep\xE8res de paire de crochets.","Couleur d\u2019arri\xE8re-plan des rep\xE8res de paire de crochets actifs (6). N\xE9cessite l\u2019activation des rep\xE8res de paire de crochets.","Couleur de bordure utilis\xE9e pour mettre en surbrillance les caract\xE8res Unicode","Couleur de fond utilis\xE9e pour mettre en \xE9vidence les caract\xE8res unicode","Indique si le texte de l'\xE9diteur a le focus (le curseur clignote)","Indique si l'\xE9diteur ou un widget de l'\xE9diteur a le focus (par exemple, le focus se trouve sur le widget de recherche)","Indique si un \xE9diteur ou une entr\xE9e de texte mis en forme a le focus (le curseur clignote)","Indique si l\u2019\xE9diteur est en lecture seule","Indique si le contexte est celui d'un \xE9diteur de diff\xE9rences","Indique si le contexte est celui d\u2019un \xE9diteur de diff\xE9rences int\xE9gr\xE9",null,"Indique si tous les fichiers de l\u2019\xE9diteur de diff\xE9rences sont r\xE9duits","Indique si l\u2019\xE9diteur de diff\xE9rences a des modifications","Indique si un bloc de code d\xE9plac\xE9 est s\xE9lectionn\xE9 pour \xEAtre compar\xE9","Indique si la visionneuse diff accessible est visible","Indique si le point d'arr\xEAt Render Side by Side ou inline de l'\xE9diteur de diff\xE9rences est atteint","Indique si le mode inline est actif","Indique si la modification est accessible en \xE9criture dans l\u2019\xE9diteur de diff\xE9rences","Indique si la modification est accessible en \xE9criture dans l\u2019\xE9diteur de diff\xE9rences","URI du document d\u2019origine","URI du document modifi\xE9","Indique si 'editor.columnSelection' est activ\xE9","Indique si du texte est s\xE9lectionn\xE9 dans l'\xE9diteur","Indique si l'\xE9diteur a plusieurs s\xE9lections","Indique si la touche Tab permet de d\xE9placer le focus hors de l'\xE9diteur","Indique si le pointage de l'\xE9diteur est visible","Indique si le pointage de l\u2019\xE9diteur est cibl\xE9","Indique si le d\xE9filement du pense-b\xEAte a le focus","Indique si le d\xE9filement du pense-b\xEAte est visible","Indique si le s\xE9lecteur de couleurs autonome est visible","Indique si le s\xE9lecteur de couleurs autonome est prioritaire","Indique si l'\xE9diteur fait partie d'un \xE9diteur plus important (par exemple Notebooks)","Identificateur de langage de l'\xE9diteur","Indique si l'\xE9diteur a un fournisseur d'\xE9l\xE9ments de compl\xE9tion","Indique si l'\xE9diteur a un fournisseur d'actions de code","Indique si l'\xE9diteur a un fournisseur d'informations CodeLens","Indique si l'\xE9diteur a un fournisseur de d\xE9finitions","Indique si l'\xE9diteur a un fournisseur de d\xE9clarations","Indique si l'\xE9diteur a un fournisseur d'impl\xE9mentation","Indique si l'\xE9diteur a un fournisseur de d\xE9finitions de type","Indique si l'\xE9diteur a un fournisseur de pointage","Indique si l'\xE9diteur a un fournisseur de mise en surbrillance pour les documents","Indique si l'\xE9diteur a un fournisseur de symboles pour les documents","Indique si l'\xE9diteur a un fournisseur de r\xE9f\xE9rence","Indique si l'\xE9diteur a un fournisseur de renommage","Indique si l'\xE9diteur a un fournisseur d'aide sur les signatures","Indique si l'\xE9diteur a un fournisseur d'indicateurs inline","Indique si l'\xE9diteur a un fournisseur de mise en forme pour les documents","Indique si l'\xE9diteur a un fournisseur de mise en forme de s\xE9lection pour les documents","Indique si l'\xE9diteur a plusieurs fournisseurs de mise en forme pour les documents","Indique si l'\xE9diteur a plusieurs fournisseurs de mise en forme de s\xE9lection pour les documents","tableau","bool\xE9en","classe","constante","constructeur","\xE9num\xE9ration","membre d'\xE9num\xE9ration","\xE9v\xE9nement","champ","fichier","fonction","interface","cl\xE9","m\xE9thode","module","espace de noms","NULL","nombre","objet","op\xE9rateur","package","propri\xE9t\xE9","cha\xEEne","struct","param\xE8tre de type","variable","{0} ({1})","Texte brut","Frappe en cours","D\xE9veloppeur\xA0: Inspecter les jetons","Acc\xE9der \xE0 la ligne/colonne...","Afficher tous les fournisseurs d'acc\xE8s rapide","Palette de commandes","Commandes d'affichage et d'ex\xE9cution","Acc\xE9der au symbole...","Acc\xE9der au symbole par cat\xE9gorie...","Contenu de l'\xE9diteur","Activer/d\xE9sactiver le th\xE8me \xE0 contraste \xE9lev\xE9","{0} modifications dans {1} fichiers","Afficher plus\xA0({0})","{0}\xA0caract\xE8res","Ancre de s\xE9lection","Ancre d\xE9finie sur {0}:{1}","D\xE9finir l'ancre de s\xE9lection","Atteindre l'ancre de s\xE9lection","S\xE9lectionner de l'ancre au curseur","Annuler l'ancre de s\xE9lection","Couleur du marqueur de la r\xE8gle d'aper\xE7u pour rechercher des parenth\xE8ses.","Atteindre le crochet","S\xE9lectionner jusqu'au crochet","Supprimer les crochets","Acc\xE9der au &&crochet","S\xE9lectionner le texte \xE0 l\u2019int\xE9rieur et inclure les crochets ou accolades","D\xE9placer le texte s\xE9lectionn\xE9 \xE0 gauche","D\xE9placer le texte s\xE9lectionn\xE9 \xE0 droite","Transposer les lettres","Co&&uper","Couper","Couper","Couper","&&Copier","Copier","Copier","Copier","Co&&ller","Coller","Coller","Coller","Copier avec la coloration syntaxique","Copier en tant que","Copier en tant que","Partager","Partager","Une erreur inconnue s'est produite \xE0 l'application de l'action du code","Type d'action de code \xE0 ex\xE9cuter.","Contr\xF4le quand les actions retourn\xE9es sont appliqu\xE9es.","Appliquez toujours la premi\xE8re action de code retourn\xE9e.","Appliquez la premi\xE8re action de code retourn\xE9e si elle est la seule.","N'appliquez pas les actions de code retourn\xE9es.","Contr\xF4le si seules les actions de code par d\xE9faut doivent \xEAtre retourn\xE9es.","Correction rapide...","Aucune action de code disponible","Aucune action de code pr\xE9f\xE9r\xE9e n'est disponible pour '{0}'","Aucune action de code disponible pour '{0}'","Aucune action de code par d\xE9faut disponible","Aucune action de code disponible","Remanier...","Aucune refactorisation par d\xE9faut disponible pour '{0}'","Aucune refactorisation disponible pour '{0}'","Aucune refactorisation par d\xE9faut disponible","Aucune refactorisation disponible","Action de la source","Aucune action source par d\xE9faut disponible pour '{0}'","Aucune action source disponible pour '{0}'","Aucune action source par d\xE9faut disponible","Aucune action n'est disponible","Organiser les importations","Aucune action organiser les imports disponible","Tout corriger","Aucune action Tout corriger disponible","Corriger automatiquement...","Aucun correctif automatique disponible","Activez/d\xE9sactivez l\u2019affichage des en-t\xEAtes de groupe dans le menu d\u2019action du code.","Activer/d\xE9sactiver l'affichage du correctif rapide le plus proche dans une ligne lorsque vous n'\xEAtes pas actuellement en cours de diagnostic.","Activez le d\xE9clenchement de {0} lorsque {1} est d\xE9fini sur {2}. Les actions de code doivent \xEAtre d\xE9finies sur {3} pour \xEAtre d\xE9clench\xE9es pour les modifications de fen\xEAtre et de focus.","Contexte\xA0: {0} \xE0 la ligne {1} et \xE0 la colonne {2}.","Masquer d\xE9sactiv\xE9","Afficher les \xE9l\xE9ments d\xE9sactiv\xE9s","Plus d\u2019actions...","Correctif rapide","Extraire","Inline","R\xE9\xE9crire","D\xE9placer","Entourer de","Action source","Ic\xF4ne qui g\xE9n\xE8re le menu Actions de code \xE0 partir de la reliure quand il n\u2019y a pas d\u2019espace dans l\u2019\xE9diteur.","Ic\xF4ne qui g\xE9n\xE8re le menu actions de code \xE0 partir de la reliure quand l\u2019\xE9diteur n\u2019a pas d\u2019espace et qu\u2019un correctif rapide est disponible.","Ic\xF4ne qui g\xE9n\xE8re le menu actions de code \xE0 partir de la reliure quand il n\u2019y a pas d\u2019espace dans l\u2019\xE9diteur et qu\u2019un correctif IA est disponible.","Ic\xF4ne qui g\xE9n\xE8re le menu actions de code \xE0 partir de la reliure quand il n\u2019y a pas d\u2019espace dans l\u2019\xE9diteur et qu\u2019un correctif IA et un correctif rapide sont disponibles.","Ic\xF4ne qui g\xE9n\xE8re le menu actions de code \xE0 partir de la reliure quand il n\u2019y a pas d\u2019espace dans l\u2019\xE9diteur et qu\u2019un correctif IA et un correctif rapide sont disponibles.","Ex\xE9cuter\xA0: {0}","Afficher les actions de code. Correctif rapide disponible par d\xE9faut ({0})","Afficher les actions de code ({0})","Afficher les actions de code","Afficher les commandes Code Lens de la ligne actuelle","S\xE9lectionner une commande",null,null,null,null,null,null,null,null,null,"Activer/d\xE9sactiver le commentaire de ligne","Afficher/masquer le commen&&taire de ligne","Ajouter le commentaire de ligne","Supprimer le commentaire de ligne","Activer/d\xE9sactiver le commentaire de bloc","Afficher/masquer le commentaire de &&bloc","Minimap","Afficher les caract\xE8res","Taille verticale","Proportionnel","Remplissage","Ajuster","Curseur","Pointer la souris","Toujours","Afficher le menu contextuel de l'\xE9diteur","Annulation du curseur","Restauration du curseur",`Type de la modification de collage \xE0 essayer pour les op\xE9rations de collage.\r
S\u2019il existe plusieurs modifications de ce type, l\u2019\xE9diteur affiche un s\xE9lecteur. S\u2019il n\u2019existe aucune modification de ce type, l\u2019\xE9diteur affiche un message d\u2019erreur.`,"Coller en tant que...","Coller au format texte","Si le widget de collage est affich\xE9","Afficher les options de collage...","Nous n\u2019avons trouv\xE9 aucune modification de collage n\u2019a \xE9t\xE9 trouv\xE9e pour \xAB\xA0{0}\xA0\xBB","R\xE9solution de la modification du collage. Cliquez ici pour annuler","Ex\xE9cution des gestionnaires de collage. Cliquez pour annuler et effectuer un collage de base","S\xE9lectionner l\u2019action Coller","Ex\xE9cution des gestionnaires de collage","Ins\xE9rer du texte brut","Ins\xE9rer des URI","Ins\xE9rer un URI","Ins\xE9rer des chemins d\u2019acc\xE8s","Ins\xE9rer un chemin d\u2019acc\xE8s","Ins\xE9rer des chemins d\u2019acc\xE8s relatifs","Ins\xE9rer un chemin d\u2019acc\xE8s relatif","Ins\xE9rer du code HTML",null,"Indique si le widget de suppression s\u2019affiche","Afficher les options de suppression...","Ex\xE9cution des gestionnaires de d\xE9p\xF4t. Cliquez pour annuler",`Erreur lors de la r\xE9solution de la modification \xAB{0}\xBB :\r
{1}`,`Erreur lors de l\u2019application de la modification \xAB{0}\xBB :\r
{1}`,"Indique si l'\xE9diteur ex\xE9cute une op\xE9ration annulable, par exemple 'Avoir un aper\xE7u des r\xE9f\xE9rences'","Le fichier est trop volumineux pour effectuer une op\xE9ration Tout remplacer.","Rechercher","&&Rechercher","Trouver avec des arguments","Rechercher dans la s\xE9lection","Rechercher suivant","Rechercher pr\xE9c\xE9dent","Acc\xE9der \xE0 la correspondance...","Aucune correspondance. Essayez de rechercher autre chose.","Tapez un nombre pour acc\xE9der \xE0 une correspondance sp\xE9cifique (entre 1 et {0})","Veuillez entrer un nombre compris entre 1 et {0}","Veuillez entrer un nombre compris entre 1 et {0}","S\xE9lection suivante","S\xE9lection pr\xE9c\xE9dente","Remplacer","&&Remplacer","Ic\xF4ne permettant d'indiquer que le widget de recherche de l'\xE9diteur est r\xE9duit.","Ic\xF4ne permettant d'indiquer que le widget de recherche de l'\xE9diteur est d\xE9velopp\xE9.","Ic\xF4ne de l'option Rechercher dans la s\xE9lection dans le widget de recherche de l'\xE9diteur.","Ic\xF4ne de l'option Remplacer dans le widget de recherche de l'\xE9diteur.","Ic\xF4ne de l'option Tout remplacer dans le widget de recherche de l'\xE9diteur.","Ic\xF4ne de l'option Rechercher pr\xE9c\xE9dent dans le widget de recherche de l'\xE9diteur.","Ic\xF4ne de l'option Rechercher suivant dans le widget de recherche de l'\xE9diteur.","Rechercher/remplacer","Rechercher","Rechercher","Correspondance pr\xE9c\xE9dente","Correspondance suivante","Rechercher dans la s\xE9lection","Fermer","Remplacer","Remplacer","Remplacer","Tout remplacer","Activer/d\xE9sactiver le remplacement","Seuls les {0} premiers r\xE9sultats sont mis en \xE9vidence, mais toutes les op\xE9rations de recherche fonctionnent sur l\u2019ensemble du texte.","{0} sur {1}","Aucun r\xE9sultat","{0} trouv\xE9(s)","{0} trouv\xE9 pour '{1}'","{0} trouv\xE9 pour '{1}', sur {2}","{0} trouv\xE9 pour '{1}'","La combinaison Ctrl+Entr\xE9e permet d\xE9sormais d'ajouter un saut de ligne au lieu de tout remplacer. Vous pouvez modifier le raccourci clavier de editor.action.replaceAll pour red\xE9finir le comportement.","D\xE9plier","D\xE9plier de mani\xE8re r\xE9cursive","Plier","Activer/d\xE9sactiver le pliage","Plier de mani\xE8re r\xE9cursive","Activer/d\xE9sactiver le repli r\xE9cursif","Replier tous les commentaires de bloc","Replier toutes les r\xE9gions","D\xE9plier toutes les r\xE9gions","Plier tout, sauf les \xE9l\xE9ments s\xE9lectionn\xE9s","D\xE9plier tout, sauf les \xE9l\xE9ments s\xE9lectionn\xE9s","Plier tout","D\xE9plier tout","Atteindre le pli parent","Acc\xE9der \xE0 la plage de pliage pr\xE9c\xE9dente","Acc\xE9der \xE0 la plage de pliage suivante","Cr\xE9er une plage de pliage \xE0 partir de la s\xE9lection","Supprimer les plages de pliage manuelles","Niveau de pliage {0}","Couleur d'arri\xE8re-plan des gammes pli\xE9es. La couleur ne doit pas \xEAtre opaque pour ne pas cacher les d\xE9corations sous-jacentes.","Couleur du texte r\xE9duit apr\xE8s la premi\xE8re ligne d\u2019une plage pli\xE9e.","Couleur du contr\xF4le de pliage dans la marge de l'\xE9diteur.","Ic\xF4ne des plages d\xE9velopp\xE9es dans la marge de glyphes de l'\xE9diteur.","Ic\xF4ne des plages r\xE9duites dans la marge de glyphes de l'\xE9diteur.","Ic\xF4ne pour les plages r\xE9duites manuellement dans la marge de glyphe de l\u2019\xE9diteur.","Ic\xF4ne pour les plages d\xE9velopp\xE9es manuellement dans la marge de glyphe de l\u2019\xE9diteur.","Cliquez pour d\xE9velopper la plage.","Cliquez pour r\xE9duire la plage.","Augmenter la taille de police de l\u2019\xE9diteur","Diminuer la taille de police de l\u2019\xE9diteur","R\xE9initialiser la taille de police de l\u2019\xE9diteur","Mettre le document en forme","Mettre la s\xE9lection en forme","Aller au probl\xE8me suivant (Erreur, Avertissement, Info)","Ic\xF4ne du prochain marqueur goto.","Aller au probl\xE8me pr\xE9c\xE9dent (Erreur, Avertissement, Info)","Ic\xF4ne du pr\xE9c\xE9dent marqueur goto.","Aller au probl\xE8me suivant dans Fichiers (Erreur, Avertissement, Info)","&&Probl\xE8me suivant","Aller au probl\xE8me pr\xE9c\xE9dent dans Fichiers (Erreur, Avertissement, Info)","&&Probl\xE8me pr\xE9c\xE9dent","Erreur","Avertissement","Info","Conseil","{0} \xE0 {1}. ","{0}\xA0probl\xE8mes sur\xA0{1}","{0}\xA0probl\xE8me(s) sur {1}","Couleur d'erreur du widget de navigation dans les marqueurs de l'\xE9diteur.","Arri\xE8re-plan du titre d\u2019erreur du widget de navigation dans les marqueurs de l\u2019\xE9diteur.","Couleur d'avertissement du widget de navigation dans les marqueurs de l'\xE9diteur.","Arri\xE8re-plan du titre d\u2019erreur du widget de navigation dans les marqueurs de l\u2019\xE9diteur.","Couleur d\u2019information du widget de navigation du marqueur de l'\xE9diteur.","Arri\xE8re-plan du titre des informations du widget de navigation dans les marqueurs de l\u2019\xE9diteur.","Arri\xE8re-plan du widget de navigation dans les marqueurs de l'\xE9diteur.","Aper\xE7u","D\xE9finitions","D\xE9finition introuvable pour '{0}'","D\xE9finition introuvable","Atteindre la &&d\xE9finition","D\xE9clarations","Aucune d\xE9claration pour '{0}'","Aucune d\xE9claration","Atteindre la &&d\xE9claration","Aucune d\xE9claration pour '{0}'","Aucune d\xE9claration","D\xE9finitions de type","D\xE9finition de type introuvable pour '{0}'","D\xE9finition de type introuvable","Acc\xE9der \xE0 la d\xE9finition de &&type","Impl\xE9mentations","Impl\xE9mentation introuvable pour '{0}'","Impl\xE9mentation introuvable","Atteindre les &&impl\xE9mentations","Aucune r\xE9f\xE9rence pour '{0}'","Aucune r\xE9f\xE9rence","Atteindre les &&r\xE9f\xE9rences","R\xE9f\xE9rences","R\xE9f\xE9rences","Emplacements","Aucun r\xE9sultat pour \xAB\xA0{0}\xA0\xBB","R\xE9f\xE9rences","Atteindre la d\xE9finition","Ouvrir la d\xE9finition sur le c\xF4t\xE9","Aper\xE7u de la d\xE9finition","Acc\xE9der \xE0 la d\xE9claration","Aper\xE7u de la d\xE9claration","Atteindre la d\xE9finition du type","Aper\xE7u de la d\xE9finition du type","Atteindre les impl\xE9mentations","Aper\xE7u des impl\xE9mentations","Atteindre les r\xE9f\xE9rences","Aper\xE7u des r\xE9f\xE9rences","Atteindre un symbole","Cliquez pour afficher {0}\xA0d\xE9finitions.","Indique si l'aper\xE7u des r\xE9f\xE9rences est visible, par exemple via 'Avoir un aper\xE7u des r\xE9f\xE9rences' ou 'Faire un peek de la d\xE9finition'","Chargement en cours...","{0} ({1})","{0} r\xE9f\xE9rences","{0} r\xE9f\xE9rence","R\xE9f\xE9rences","aper\xE7u non disponible","Aucun r\xE9sultat","R\xE9f\xE9rences","dans {0} \xE0 la ligne {1} \xE0 la colonne {2}","{0}dans {1} \xE0 la ligne {2} \xE0 la colonne {3}","1 symbole dans {0}, chemin complet {1}","{0} symboles dans {1}, chemin complet {2}","R\xE9sultats introuvables","1\xA0symbole dans {0}","{0}\xA0symboles dans {1}","{0}\xA0symboles dans {1} fichiers","Indique s'il existe des emplacements de symboles que vous pouvez parcourir \xE0 l'aide du clavier uniquement.","Symbole {0} sur {1}, {2} pour le suivant","Symbole {0} sur {1}","Augmenter le niveau de verbosit\xE9 par pointage","Diminuer le niveau de d\xE9tail du pointage","Afficher ou focus sur pointer","Le pointage ne prend pas automatiquement le focus.","Le pointage prend le focus uniquement s\u2019il est d\xE9j\xE0 visible.","Le pointage prend automatiquement le focus lorsqu\u2019il appara\xEEt.","Afficher le pointeur de l'aper\xE7u de d\xE9finition","Faire d\xE9filer le pointage vers le haut","Faire d\xE9filer le pointage vers le bas","Faire d\xE9filer vers la gauche au pointage","Faire d\xE9filer le pointage vers la droite","Pointer vers le haut de la page","Pointer vers le bas de la page","Atteindre le pointage sup\xE9rieur","Pointer vers le bas","Affichez ou concentrez le pointeur de l\u2019\xE9diteur qui affiche la documentation, les r\xE9f\xE9rences et d\u2019autres contenus pour un symbole \xE0 la position actuelle du curseur.","Afficher l\u2019aper\xE7u de d\xE9finition survol\xE9 dans l\u2019\xE9diteur.","Faites d\xE9filer vers le haut le pointage de l\u2019\xE9diteur.","Faites d\xE9filer le pointeur de l\u2019\xE9diteur vers le bas.","Faites d\xE9filer vers la gauche le pointeur de l\u2019\xE9diteur.","Faites d\xE9filer vers la droite le pointeur de l\u2019\xE9diteur.","Page pr\xE9c\xE9dente le pointage de l\u2019\xE9diteur.","Page suivante le pointage de l\u2019\xE9diteur.","Acc\xE9dez au haut du pointage de l\u2019\xE9diteur.","Atteindre le bas du pointage de l\u2019\xE9diteur.","Ic\xF4ne permettant d\u2019augmenter le niveau de d\xE9tail du pointage.","Ic\xF4ne permettant de r\xE9duire le niveau de d\xE9tail du pointage.","Chargement en cours...","Rendu suspendu pour une longue ligne pour des raisons de performances. Cela peut \xEAtre configur\xE9 via 'editor.stopRenderingLineAfter'.","La tokenisation des lignes longues est ignor\xE9e pour des raisons de performances. Cela peut \xEAtre configur\xE9e via 'editor.maxTokenizationLineLength'.","Augmenter la verbosit\xE9 par pointage ({0})","Augmenter la verbosit\xE9 par pointage","Diminuer la verbosit\xE9 par pointage ({0})","Diminuer la verbosit\xE9 par pointage","Voir le probl\xE8me","Aucune solution disponible dans l'imm\xE9diat","Recherche de correctifs rapides...","Aucune solution disponible dans l'imm\xE9diat","Correction rapide...","Convertir les retraits en espaces","Convertir les retraits en tabulations","Taille des tabulations configur\xE9e","Taille des tabulations par d\xE9faut","Taille actuelle des tabulations","S\xE9lectionner la taille des tabulations pour le fichier actuel","Mettre en retrait avec des tabulations","Mettre en retrait avec des espaces","Modifier la taille d\u2019affichage des tabulations","D\xE9tecter la mise en retrait \xE0 partir du contenu","Remettre en retrait les lignes","R\xE9indenter les lignes s\xE9lectionn\xE9es","Convertir la mise en retrait de l\u2019onglet en espaces.","Convertissez la mise en retrait des espaces en onglets.","Utilisez une mise en retrait avec des onglets.","Utilisez la mise en retrait avec des espaces.","Modifier la taille d\u2019espace \xE9quivalente \xE0 l\u2019onglet.","D\xE9tectez la mise en retrait du contenu.","R\xE9ins\xE9rez les lignes de l\u2019\xE9diteur.","R\xE9ins\xE9rez les lignes s\xE9lectionn\xE9es de l\u2019\xE9diteur.","Double-cliquer pour ins\xE9rer","cmd + clic","ctrl + clic","option + clic","alt + clic","Acc\xE9dez \xE0 D\xE9finition ({0}), cliquez avec le bouton droit pour en savoir plus.","Acc\xE9der \xE0 D\xE9finition ({0})","Ex\xE9cuter la commande","Afficher la suggestion en ligne suivante","Afficher la suggestion en ligne pr\xE9c\xE9dente","D\xE9clencher une suggestion en ligne","Accepter le mot suivant de la suggestion inline","Accepter Word","Accepter la ligne suivante d\u2019une suggestion en ligne","Accepter la ligne","Accepter la suggestion en ligne","Accepter","Masquer la suggestion en ligne","Toujours afficher la barre d'outils","Indique si une suggestion en ligne est visible","Indique si la suggestion en ligne commence par un espace blanc","Indique si la suggestion incluse commence par un espace blanc inf\xE9rieur \xE0 ce qui serait ins\xE9r\xE9 par l\u2019onglet.","Indique si les suggestions doivent \xEAtre supprim\xE9es pour la suggestion actuelle","Inspecter ceci dans l\u2019affichage accessible ({0})","Suggestion :","Ic\xF4ne d'affichage du prochain conseil de param\xE8tre.","Ic\xF4ne d'affichage du pr\xE9c\xE9dent conseil de param\xE8tre.","{0} ({1})","Pr\xE9c\xE9dent","Suivant",null,null,null,null,null,null,null,null,"Remplacer par la valeur pr\xE9c\xE9dente","Remplacer par la valeur suivante","D\xE9velopper la s\xE9lection de ligne","Copier la ligne en haut","&&Copier la ligne en haut","Copier la ligne en bas","Co&&pier la ligne en bas","Dupliquer la s\xE9lection","&&Dupliquer la s\xE9lection","D\xE9placer la ligne vers le haut","D\xE9placer la ligne &&vers le haut","D\xE9placer la ligne vers le bas","D\xE9placer la &&ligne vers le bas","Trier les lignes dans l'ordre croissant","Trier les lignes dans l'ordre d\xE9croissant","Supprimer les lignes dupliqu\xE9es","D\xE9couper l'espace blanc de fin","Supprimer la ligne","Mettre en retrait la ligne","Ajouter un retrait n\xE9gatif \xE0 la ligne","Ins\xE9rer une ligne au-dessus","Ins\xE9rer une ligne sous","Supprimer tout ce qui est \xE0 gauche","Supprimer tout ce qui est \xE0 droite","Joindre les lignes","Transposer des caract\xE8res autour du curseur","Transformer en majuscule","Transformer en minuscule",'Appliquer la casse "1re lettre des mots en majuscule"',"Transformer en snake case","Transformer en casse mixte","Transformer en casse Pascal","Transformer en kebab case","D\xE9marrer la modification li\xE9e","Couleur d'arri\xE8re-plan quand l'\xE9diteur renomme automatiquement le type.","\xC9chec de l'ouverture de ce lien, car il n'est pas bien form\xE9\xA0: {0}","\xC9chec de l'ouverture de ce lien, car sa cible est manquante.","Ex\xE9cuter la commande","suivre le lien","cmd + clic","ctrl + clic","option + clic","alt + clic","Ex\xE9cuter la commande {0}","Ouvrir le lien","Indique si l'\xE9diteur affiche un message inline","Curseur ajout\xE9\xA0: {0}","Curseurs ajout\xE9s\xA0: {0}","Ajouter un curseur au-dessus","&&Ajouter un curseur au-dessus","Ajouter un curseur en dessous","Aj&&outer un curseur en dessous","Ajouter des curseurs \xE0 la fin des lignes","Ajouter des c&&urseurs \xE0 la fin des lignes","Ajouter des curseurs en bas","Ajouter des curseurs en haut","Ajouter la s\xE9lection \xE0 la correspondance de recherche suivante","Ajouter l'occurrence suiva&&nte","Ajouter la s\xE9lection \xE0 la correspondance de recherche pr\xE9c\xE9dente","Ajouter l'occurrence p&&r\xE9c\xE9dente","D\xE9placer la derni\xE8re s\xE9lection vers la correspondance de recherche suivante","D\xE9placer la derni\xE8re s\xE9lection \xE0 la correspondance de recherche pr\xE9c\xE9dente","S\xE9lectionner toutes les occurrences des correspondances de la recherche","S\xE9lectionner toutes les &&occurrences","Modifier toutes les occurrences","Focus sur le curseur suivant","Concentre le curseur suivant","Focus sur le curseur pr\xE9c\xE9dent","Concentre le curseur pr\xE9c\xE9dent","Indicateurs des param\xE8tres Trigger","Ic\xF4ne d'affichage du prochain conseil de param\xE8tre.","Ic\xF4ne d'affichage du pr\xE9c\xE9dent conseil de param\xE8tre.","{0}, conseil","Couleur de premier plan de l\u2019\xE9l\xE9ment actif dans l\u2019indicateur de param\xE8tre.","Indique si l'\xE9diteur de code actuel est int\xE9gr\xE9 \xE0 l'aper\xE7u","Fermer","Couleur d'arri\xE8re-plan de la zone de titre de l'affichage d'aper\xE7u.","Couleur du titre de l'affichage d'aper\xE7u.","Couleur des informations sur le titre de l'affichage d'aper\xE7u.","Couleur des bordures et de la fl\xE8che de l'affichage d'aper\xE7u.","Couleur d'arri\xE8re-plan de la liste des r\xE9sultats de l'affichage d'aper\xE7u.","Couleur de premier plan des noeuds de lignes dans la liste des r\xE9sultats de l'affichage d'aper\xE7u.","Couleur de premier plan des noeuds de fichiers dans la liste des r\xE9sultats de l'affichage d'aper\xE7u.","Couleur d'arri\xE8re-plan de l'entr\xE9e s\xE9lectionn\xE9e dans la liste des r\xE9sultats de l'affichage d'aper\xE7u.","Couleur de premier plan de l'entr\xE9e s\xE9lectionn\xE9e dans la liste des r\xE9sultats de l'affichage d'aper\xE7u.","Couleur d'arri\xE8re-plan de l'\xE9diteur d'affichage d'aper\xE7u.","Couleur d'arri\xE8re-plan de la bordure de l'\xE9diteur d'affichage d'aper\xE7u.","Couleur d\u2019arri\xE8re-plan du d\xE9filement r\xE9manent dans l\u2019\xE9diteur d\u2019affichage d\u2019aper\xE7u.","Couleur de mise en surbrillance d'une correspondance dans la liste des r\xE9sultats de l'affichage d'aper\xE7u.","Couleur de mise en surbrillance d'une correspondance dans l'\xE9diteur de l'affichage d'aper\xE7u.","Bordure de mise en surbrillance d'une correspondance dans l'\xE9diteur de l'affichage d'aper\xE7u.","Couleur de premier plan du texte texte de l\u2019espace r\xE9serv\xE9 dans l\u2019\xE9diteur.","Ouvrez d'abord un \xE9diteur de texte pour acc\xE9der \xE0 une ligne.","Atteindre la ligne {0} et le caract\xE8re {1}.","Acc\xE9dez \xE0 la ligne {0}.","Ligne actuelle\xA0: {0}, caract\xE8re\xA0: {1}. Tapez un num\xE9ro de ligne entre\xA01 et\xA0{2} auquel acc\xE9der.","Ligne actuelle\xA0: {0}, caract\xE8re\xA0: {1}. Tapez un num\xE9ro de ligne auquel acc\xE9der.","Pour acc\xE9der \xE0 un symbole, ouvrez d'abord un \xE9diteur de texte avec des informations de symbole.","L'\xE9diteur de texte actif ne fournit pas les informations de symbole.","Aucun symbole d'\xE9diteur correspondant","Aucun symbole d'\xE9diteur","Ouvrir sur le c\xF4t\xE9","Ouvrir en bas","symboles ({0})","propri\xE9t\xE9s ({0})","m\xE9thodes ({0})","fonctions ({0})","constructeurs ({0})","variables ({0})","classes ({0})","structs ({0})","\xE9v\xE9nements ({0})","op\xE9rateurs ({0})","interfaces ({0})","espaces de noms ({0})","packages ({0})","param\xE8tres de type ({0})","modules ({0})","propri\xE9t\xE9s ({0})","\xE9num\xE9rations ({0})","membres d'\xE9num\xE9ration ({0})","cha\xEEnes ({0})","fichiers ({0})","tableaux ({0})","nombres ({0})","bool\xE9ens ({0})","objets ({0})","cl\xE9s ({0})","champs ({0})","constantes ({0})","Impossible de modifier dans l\u2019entr\xE9e en lecture seule","Impossible de modifier dans l\u2019\xE9diteur en lecture seule","Aucun r\xE9sultat.","Une erreur inconnue s'est produite lors de la r\xE9solution de l'emplacement de renommage","Renommage de '{0}' en '{1}'","Changement du nom de {0} en {1}","'{0}' renomm\xE9 en '{1}'. R\xE9capitulatif : {2}","Le renommage n'a pas pu appliquer les modifications","Le renommage n'a pas pu calculer les modifications","Renommer le symbole","Activer/d\xE9sactiver la possibilit\xE9 d'afficher un aper\xE7u des changements avant le renommage","Prioriser la prochaine suggestion de changement de nom","Prioriser la suggestion de changement de nom pr\xE9c\xE9dente","Indique si le widget de renommage d'entr\xE9e est visible","Indique si le widget de renommage d'entr\xE9e est prioritaire","{0} pour renommer, {1} pour afficher un aper\xE7u","{0} suggestions de changement de nom re\xE7ues","Renommez l'entr\xE9e. Tapez le nouveau nom et appuyez sur Entr\xE9e pour valider.","G\xE9n\xE9rer des suggestions de nom","Annuler","\xC9tendre la s\xE9lection","D\xE9v&&elopper la s\xE9lection","R\xE9duire la s\xE9lection","&&R\xE9duire la s\xE9lection","Indique si l'\xE9diteur est actualis\xE9 en mode extrait","Indique s'il existe un taquet de tabulation suivant en mode extrait","Indique s'il existe un taquet de tabulation pr\xE9c\xE9dent en mode extrait","Acc\xE9der \xE0 l\u2019espace r\xE9serv\xE9 suivant...","Dimanche","Lundi","Mardi","Mercredi","Jeudi","Vendredi","Samedi","Dim","Lun","Mar","Mer","Jeu","Ven","Sam","Janvier","F\xE9vrier","Mars","Avril","Mai","Juin","Juillet","Ao\xFBt","Septembre","Octobre","Novembre","D\xE9cembre","Jan","F\xE9v","Mar","Avr","Mai","Juin","Jul","Ao\xFB","Sept","Oct","Nov","D\xE9c","&&Activer/d\xE9sactiver le d\xE9filement \xE9pingl\xE9 de l\u2019\xE9diteur","D\xE9filement \xE9pingl\xE9","&&D\xE9filement \xE9pingl\xE9","&&Focus sur le d\xE9filement du pense-b\xEAte","Activer/d\xE9sactiver le d\xE9filement \xE9pingl\xE9 de l\u2019\xE9diteur","Basculer/activer le d\xE9filement r\xE9manent de l\u2019\xE9diteur qui affiche les \xE9tendues imbriqu\xE9s en haut de la fen\xEAtre d\u2019affichage","Focus sur le d\xE9filement de l\u2019\xE9diteur","S\xE9lectionner la ligne de d\xE9filement collante de l\u2019\xE9diteur suivant","S\xE9lectionner la ligne de d\xE9filement du pense-b\xEAte pr\xE9c\xE9dente","Atteindre la ligne de d\xE9filement pense-b\xEAte prioritaire","S\xE9lectionner l'\xE9diteur","Indique si une suggestion a le focus","Indique si les d\xE9tails des suggestions sont visibles","Indique s'il existe plusieurs suggestions au choix","Indique si l'insertion de la suggestion actuelle entra\xEEne un changement ou si tout a d\xE9j\xE0 \xE9t\xE9 tap\xE9","Indique si les suggestions sont ins\xE9r\xE9es quand vous appuyez sur Entr\xE9e","Indique si la suggestion actuelle a un comportement d'insertion et de remplacement","Indique si le comportement par d\xE9faut consiste \xE0 ins\xE9rer ou \xE0 remplacer","Indique si la suggestion actuelle prend en charge la r\xE9solution des d\xE9tails suppl\xE9mentaires","L'acceptation de '{0}' a entra\xEEn\xE9 {1}\xA0modifications suppl\xE9mentaires","Suggestions pour Trigger","Ins\xE9rer","Ins\xE9rer","Remplacer","Remplacer","Ins\xE9rer","Afficher moins","Afficher plus","R\xE9initialiser la taille du widget de suggestion","Couleur d'arri\xE8re-plan du widget de suggestion.","Couleur de bordure du widget de suggestion.","Couleur de premier plan du widget de suggestion.","Couleur de premier plan de l\u2019entr\xE9e s\xE9lectionn\xE9e dans le widget de suggestion.","Couleur de premier plan de l\u2019ic\xF4ne de l\u2019entr\xE9e s\xE9lectionn\xE9e dans le widget de suggestion.","Couleur d'arri\xE8re-plan de l'entr\xE9e s\xE9lectionn\xE9e dans le widget de suggestion.","Couleur de la surbrillance des correspondances dans le widget de suggestion.","Couleur des mises en surbrillance dans le widget de suggestion lorsqu\u2019un \xE9l\xE9ment a le focus.","Couleur de premier plan du statut du widget de suggestion.","Chargement en cours...","Pas de suggestions.","Sugg\xE9rer","{0} {1}, {2}","{0} {1}","{0}, {1}","{0}, documents\xA0: {1}","Fermer","Chargement en cours...","Ic\xF4ne d'affichage d'informations suppl\xE9mentaires dans le widget de suggestion.","Lire la suite","Couleur de premier plan des symboles de tableau. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles bool\xE9ens. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de classe. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de couleur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan pour les symboles de constante. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de constructeur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'\xE9num\xE9rateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de membre d'\xE9num\xE9rateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'\xE9v\xE9nement. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de champ. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de fichier. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de dossier. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de fonction. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'interface. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de cl\xE9. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de mot cl\xE9. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de m\xE9thode. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de module. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'espace de noms. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles null. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de nombre. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'objet. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'op\xE9rateur. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de package. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de propri\xE9t\xE9. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de r\xE9f\xE9rence. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'extrait de code. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de cha\xEEne. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de struct. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de texte. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de param\xE8tre de type. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles d'unit\xE9. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Couleur de premier plan des symboles de variable. Ces symboles apparaissent dans le plan, la barre de navigation et le widget de suggestion.","Appuyer sur Tab d\xE9placera le focus vers le prochain \xE9l\xE9ment pouvant \xEAtre d\xE9sign\xE9 comme \xE9l\xE9ment actif","Appuyer sur Tab ins\xE9rera le caract\xE8re de tabulation","Activer/d\xE9sactiver l'utilisation de la touche Tab pour d\xE9placer le focus","D\xE9termine si la touche d\u2019onglet d\xE9place le focus autour du workbench ou ins\xE8re le caract\xE8re d\u2019onglet dans l\u2019\xE9diteur actuel. Il s\u2019agit \xE9galement du verrouillage des onglets, de la navigation dans les onglets ou du mode focus des onglets.","D\xE9veloppeur\xA0: forcer la retokenisation","Ic\xF4ne affich\xE9e avec un message d'avertissement dans l'\xE9diteur d'extensions.","Ce document contient de nombreux caract\xE8res Unicode ASCII non basiques.","Ce document contient de nombreux caract\xE8res Unicode ambigus.","Ce document contient de nombreux caract\xE8res Unicode invisibles.","Configurer les options de surlignage Unicode","Le caract\xE8re {0} peut \xEAtre confondu avec le caract\xE8re ASCII {1}, qui est plus courant dans le code source.","Le caract\xE8re {0} peut \xEAtre confus avec le caract\xE8re {1}, ce qui est plus courant dans le code source.","Le caract\xE8re {0} est invisible.","Le caract\xE8re {0} n\u2019est pas un caract\xE8re ASCII de base.","Ajuster les param\xE8tres","D\xE9sactiver la mise en surbrillance dans les commentaires","D\xE9sactiver la mise en surbrillance des caract\xE8res dans les commentaires","D\xE9sactiver la mise en surbrillance dans les cha\xEEnes","D\xE9sactiver la mise en surbrillance des caract\xE8res dans les cha\xEEnes","D\xE9sactiver la mise en surbrillance ambigu\xEB","D\xE9sactiver la mise en surbrillance des caract\xE8res ambigus","D\xE9sactiver le surlignage invisible","D\xE9sactiver la mise en surbrillance des caract\xE8res invisibles","D\xE9sactiver la mise en surbrillance non ASCII","D\xE9sactiver la mise en surbrillance des caract\xE8res ASCII non de base","Afficher les options d\u2019exclusion","Exclure la mise en surbrillance des {0} (caract\xE8re invisible)","Exclure {0} de la mise en surbrillance",'Autoriser les caract\xE8res Unicode plus courants dans le langage "{0}"',"Marques de fin de ligne inhabituelles","Marques de fin de ligne inhabituelles d\xE9tect\xE9es","Le fichier \xAB\xA0{0}\xA0\xBBcontient un ou plusieurs caract\xE8res de fin de ligne inhabituels, par exemple le s\xE9parateur de ligne (LS) ou le s\xE9parateur de paragraphe (PS).\r\n\r\nIl est recommand\xE9 de les supprimer du fichier. Vous pouvez configurer ce comportement par le biais de `editor.unusualLineTerminators`.","&&Supprimer les marques de fin de ligne inhabituelles","Ignorer","Couleur d'arri\xE8re-plan d'un symbole pendant l'acc\xE8s en lecture, comme la lecture d'une variable. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arri\xE8re-plan d'un symbole pendant l'acc\xE8s en \xE9criture, comme l'\xE9criture d'une variable. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur d\u2019arri\xE8re-plan d\u2019une occurrence textuelle d\u2019un symbole. La couleur ne doit pas \xEAtre opaque afin de ne pas masquer les d\xE9corations sous-jacentes.","Couleur de bordure d'un symbole durant l'acc\xE8s en lecture, par exemple la lecture d'une variable.","Couleur de bordure d'un symbole durant l'acc\xE8s en \xE9criture, par exemple l'\xE9criture dans une variable.","Couleur de bordure d\u2019une occurrence textuelle pour un symbole.","Couleur de marqueur de la r\xE8gle d'aper\xE7u pour la mise en surbrillance des symboles. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur de marqueur de la r\xE8gle d'aper\xE7u pour la mise en surbrillance des symboles d'acc\xE8s en \xE9criture. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur de marqueur de r\xE8gle d\u2019aper\xE7u d\u2019une occurrence textuelle pour un symbole. La couleur ne doit pas \xEAtre opaque afin de ne pas masquer les d\xE9corations sous-jacentes.","Aller \xE0 la prochaine mise en \xE9vidence de symbole","Aller \xE0 la mise en \xE9vidence de symbole pr\xE9c\xE9dente","D\xE9clencher la mise en \xE9vidence de symbole","Supprimer le mot","Erreur sur la position","Erreur","Avertissement \xE0 la position","Avertissement","Erreur sur la ligne","Erreur sur la ligne","Avertissement sur la ligne","Avertissement sur la ligne","Zone pli\xE9e sur la ligne","Repli\xE9","Point d\u2019arr\xEAt sur ligne","Point d\u2019arr\xEAt","Suggestion inline sur la ligne","Correctif rapide de terminal","Correctif rapide","D\xE9bogueur arr\xEAt\xE9 sur le point d\u2019arr\xEAt","Point d\u2019arr\xEAt","Aucun indicateur d\u2019inlay sur la ligne","Aucun conseil Inlay","T\xE2che termin\xE9e","T\xE2che termin\xE9e","\xC9chec de la t\xE2che","\xC9chec de la t\xE2che","\xC9chec de la commande de terminal","\xC9chec de la commande","Commande terminal r\xE9ussie","Commande r\xE9ussie","Cloche de terminal","Cloche de terminal","Cellule de bloc-notes termin\xE9e","Cellule de bloc-notes termin\xE9e","\xC9chec de la cellule de bloc-notes","\xC9chec de la cellule de bloc-notes","Ligne de diffusion ins\xE9r\xE9e","Ligne de diffusion supprim\xE9e","Ligne diff modifi\xE9e","Demande de conversation envoy\xE9e","Requ\xEAte de conversation envoy\xE9e","R\xE9ponse de conversation re\xE7ue","Progression","Progression","Effacer","Effacer","Enregistrer","Enregistrer","Format","Format","Enregistrement vocal commenc\xE9","Enregistrement vocal arr\xEAt\xE9","Afficher","Aide","Test","fichier","Pr\xE9f\xE9rences","D\xE9veloppeur","{0} ({1})","{0} ({1})",`{0}\r
[{1}] {2}`,"Du {1} au {0}","{0} ({1})","Masquer","R\xE9initialiser le menu","Masquer \xAB{0}\xBB","Configurer la combinaison de touches","{0} \xE0 Appliquer, {1} \xE0 la Pr\xE9version","{0} pour appliquer","{0}, raison d\xE9sactiv\xE9e : {1}","Widget d\u2019action","Couleur d'arri\xE8re-plan des \xE9l\xE9ments d'action activ\xE9s dans la barre d'action.","Indique si la liste des widgets d\u2019action est visible","Masquer le widget d\u2019action","S\xE9lectionner l\u2019action pr\xE9c\xE9dente","S\xE9lectionner l\u2019action suivante","Accepter l\u2019action s\xE9lectionn\xE9e","Aper\xE7u de l\u2019action s\xE9lectionn\xE9e","Substitutions de configuration du langage par d\xE9faut","Configurez les param\xE8tres \xE0 remplacer pour le langage {0}.","Configurez les param\xE8tres d'\xE9diteur \xE0 remplacer pour un langage.","Ce param\xE8tre ne prend pas en charge la configuration par langage.","Configurez les param\xE8tres d'\xE9diteur \xE0 remplacer pour un langage.","Ce param\xE8tre ne prend pas en charge la configuration par langage.","Impossible d'inscrire une propri\xE9t\xE9 vide","Impossible d'inscrire '{0}'. Ceci correspond au mod\xE8le de propri\xE9t\xE9 '\\\\[.*\\\\]$' permettant de d\xE9crire les param\xE8tres d'\xE9diteur sp\xE9cifiques \xE0 un langage. Utilisez la contribution 'configurationDefaults'.","Impossible d'inscrire '{0}'. Cette propri\xE9t\xE9 est d\xE9j\xE0 inscrite.","Impossible d\u2019inscrire '{0}'. Le {1} de strat\xE9gie associ\xE9 est d\xE9j\xE0 inscrit aupr\xE8s de {2}.","Commande qui retourne des informations sur les cl\xE9s de contexte","Expression de cl\xE9 de contexte vide","Avez-vous oubli\xE9 d\u2019\xE9crire une expression ? Vous pouvez \xE9galement placer 'false' ou 'true' pour toujours donner la valeur false ou true, respectivement.","'in' apr\xE8s 'not'.","parenth\xE8se fermante ')'","Jeton inattendu","Avez-vous oubli\xE9 de placer && ou || avant le jeton ?","Fin d\u2019expression inattendue","Avez-vous oubli\xE9 de placer une cl\xE9 de contexte ?",`Attendu : {0}\r
Re\xE7u : '{1}'.`,"Indique si le syst\xE8me d'exploitation est macOS","Indique si le syst\xE8me d'exploitation est Linux","Indique si le syst\xE8me d'exploitation est Windows","Indique si la plateforme est un navigateur web","Indique si le syst\xE8me d'exploitation est macOS sur une plateforme qui n'est pas un navigateur","Indique si le syst\xE8me d\u2019exploitation est Linux","Indique si la plateforme est un navigateur web mobile","Type de qualit\xE9 de VS Code","Indique si le focus clavier se trouve dans une zone d'entr\xE9e","Voulez-vous dire {0}?","Voulez-vous dire {0} ou {1}?","Voulez-vous dire {0}, {1} ou {2}?","Avez-vous oubli\xE9 d\u2019ouvrir ou de fermer le devis ?","Avez-vous oubli\xE9 d\u2019\xE9chapper le caract\xE8re \xAB / \xBB (barre oblique) ? Placez deux barre obliques inverses avant d\u2019y \xE9chapper, par ex., \xAB \\\\/ \xBB.","Indique si les suggestions sont visibles","Touche ({0}) utilis\xE9e. En attente d'une seconde touche...","({0}) a \xE9t\xE9 enfonc\xE9. En attente de la touche suivante de la pression...","La combinaison de touches ({0}, {1}) n\u2019est pas une commande.","La combinaison de touches ({0}, {1}) n\u2019est pas une commande.","Banc d'essai","Mappe vers 'Contr\xF4le' dans Windows et Linux, et vers 'Commande' dans macOS.","Mappe vers 'Alt' dans Windows et Linux, et vers 'Option' dans macOS.","Le modificateur \xE0 utiliser pour ajouter un \xE9l\xE9ment dans les arbres et listes pour une s\xE9lection multiple avec la souris (par exemple dans l\u2019Explorateur, les \xE9diteurs ouverts et la vue scm). Les mouvements de la souris 'Ouvrir \xE0 c\xF4t\xE9' (si pris en charge) s'adapteront tels qu\u2019ils n'entrent pas en conflit avec le modificateur multiselect.","Contr\xF4le l'ouverture des \xE9l\xE9ments dans les arborescences et les listes \xE0 l'aide de la souris (si cela est pris en charge). Notez que certaines arborescences et listes peuvent choisir d'ignorer ce param\xE8tre, s'il est non applicable.","Contr\xF4le si les listes et les arborescences prennent en charge le d\xE9filement horizontal dans le banc d'essai. Avertissement : L'activation de ce param\xE8tre a un impact sur les performances.","Contr\xF4le si les clics dans la barre de d\xE9filement page par page.","Contr\xF4le la mise en retrait de l'arborescence, en pixels.","Contr\xF4le si l'arborescence doit afficher les rep\xE8res de mise en retrait.","D\xE9termine si les listes et les arborescences ont un d\xE9filement fluide.","Un multiplicateur \xE0 utiliser sur les `deltaX` et `deltaY` des \xE9v\xE9nements de d\xE9filement de roulette de souris.","Multiplicateur de vitesse de d\xE9filement quand vous appuyez sur 'Alt'.","Mettez en surbrillance les \xE9l\xE9ments lors de la recherche. La navigation vers le haut et le bas traverse uniquement les \xE9l\xE9ments en surbrillance.","Filtrez des \xE9l\xE9ments lors de la recherche.","Contr\xF4le le mode de recherche par d\xE9faut pour les listes et les arborescences dans Workbench.","La navigation au clavier Simple place le focus sur les \xE9l\xE9ments qui correspondent \xE0 l'entr\xE9e de clavier. La mise en correspondance est effectu\xE9e sur les pr\xE9fixes uniquement.","La navigation de mise en surbrillance au clavier met en surbrillance les \xE9l\xE9ments qui correspondent \xE0 l'entr\xE9e de clavier. La navigation ult\xE9rieure vers le haut ou vers le bas parcourt uniquement les \xE9l\xE9ments mis en surbrillance.","La navigation au clavier Filtrer filtre et masque tous les \xE9l\xE9ments qui ne correspondent pas \xE0 l'entr\xE9e de clavier.","Contr\xF4le le style de navigation au clavier pour les listes et les arborescences dans le banc d'essai. Les options sont Simple, Mise en surbrillance et Filtrer.","Utilisez 'workbench.list.defaultFindMode' et 'workbench.list.typeNavigationMode' \xE0 la place.","Utilisez la correspondance approximative lors de la recherche.","Utilisez des correspondances contigu\xEBs lors de la recherche.","Contr\xF4le le type de correspondance utilis\xE9 lors de la recherche de listes et d\u2019arborescences dans le banc d\u2019essai.","Contr\xF4le la fa\xE7on dont les dossiers de l'arborescence sont d\xE9velopp\xE9s quand vous cliquez sur les noms de dossiers. Notez que certaines arborescences et listes peuvent choisir d'ignorer ce param\xE8tre, s'il est non applicable.","Contr\xF4le si le d\xE9filement r\xE9manent est activ\xE9 dans les arborescences.","Contr\xF4le le nombre d\u2019\xE9l\xE9ments r\xE9manents affich\xE9s dans l\u2019arborescence lorsque {0} est activ\xE9.","Contr\xF4le le fonctionnement de la navigation de type dans les listes et les arborescences du banc d'essai. Quand la valeur est 'trigger', la navigation de type commence une fois que la commande 'list.triggerTypeNavigation' est ex\xE9cut\xE9e.","Erreur","Avertissement","Info","r\xE9cemment utilis\xE9es","commandes similaires","utilis\xE9s le plus souvent","autres commandes","commandes similaires","{0}, {1}","La commande \xAB\xA0{0}\xA0\xBB a entra\xEEn\xE9 une erreur","{0}, {1}","Indique si le focus clavier se trouve \xE0 l\u2019int\xE9rieur du contr\xF4le d\u2019entr\xE9e rapide","Type de l\u2019entr\xE9e rapide actuellement visible","Indique si le curseur de l\u2019entr\xE9e rapide se trouve \xE0 la fin de la zone d\u2019entr\xE9e","Pr\xE9c\xE9dent","Appuyez sur 'Entr\xE9e' pour confirmer votre saisie, ou sur '\xC9chap' pour l'annuler","{0}/{1}","Taper pour affiner les r\xE9sultats.","Utilis\xE9 dans le contexte de la s\xE9lection rapide. Si vous modifiez une combinaison de touches pour cette commande, vous devez \xE9galement modifier toutes les autres combinaisons de touches (variantes de modification) de cette commande.","Si nous sommes en mode d\u2019acc\xE8s rapide, vous acc\xE9dez \xE0 l\u2019\xE9l\xE9ment suivant. Si nous ne sommes pas en mode d\u2019acc\xE8s rapide, cela permet d\u2019acc\xE9der au s\xE9parateur suivant.","Si nous sommes en mode d\u2019acc\xE8s rapide, vous acc\xE9dez \xE0 l\u2019\xE9l\xE9ment pr\xE9c\xE9dent. Si nous ne sommes pas en mode d\u2019acc\xE8s rapide, cela permet d\u2019acc\xE9der au s\xE9parateur pr\xE9c\xE9dent.","Activer/d\xE9sactiver toutes les cases \xE0 cocher","{0}\xA0r\xE9sultats","{0} S\xE9lectionn\xE9s","OK","Personnalis\xE9","Pr\xE9c\xE9dent ({0})","Retour","Entr\xE9e rapide","Cliquer pour ex\xE9cuter la commande '{0}'","Couleur de premier plan globale. Cette couleur est utilis\xE9e si elle n'est pas remplac\xE9e par un composant.","Premier plan globale pour les \xE9l\xE9ments d\xE9sactiv\xE9s. Cette couleur est utilis\xE9e si elle n'est pas remplac\xE9e par un composant.","Couleur principale de premier plan pour les messages d'erreur. Cette couleur est utilis\xE9e uniquement si elle n'est pas red\xE9finie par un composant.","Couleur de premier plan du texte descriptif fournissant des informations suppl\xE9mentaires, par exemple pour un label.","Couleur par d\xE9faut des ic\xF4nes du banc d'essai.","Couleur de bordure globale des \xE9l\xE9ments ayant le focus. Cette couleur est utilis\xE9e si elle n'est pas remplac\xE9e par un composant.","Bordure suppl\xE9mentaire autour des \xE9l\xE9ments pour les s\xE9parer des autres et obtenir un meilleur contraste.","Bordure suppl\xE9mentaire autour des \xE9l\xE9ments actifs pour les s\xE9parer des autres et obtenir un meilleur contraste.","La couleur d'arri\xE8re-plan des s\xE9lections de texte dans le banc d'essai (par ex., pour les champs d'entr\xE9e ou les zones de texte). Notez que cette couleur ne s'applique pas aux s\xE9lections dans l'\xE9diteur et le terminal.","Couleur des liens dans le texte.","Couleur de premier plan pour les liens dans le texte lorsqu'ils sont cliqu\xE9s ou survol\xE9s.","Couleur pour les s\xE9parateurs de texte.","Couleur des segments de texte pr\xE9format\xE9s.","Couleur d'arri\xE8re-plan pour les segments de texte pr\xE9format\xE9s.","Couleur d'arri\xE8re-plan des citations dans le texte.","Couleur de bordure des citations dans le texte.","Couleur d'arri\xE8re-plan des blocs de code dans le texte.","Couleur de premier plan utilis\xE9e dans les graphiques.","Couleur utilis\xE9e pour les lignes horizontales dans les graphiques.","Couleur rouge utilis\xE9e dans les visualisations de graphiques.","Couleur bleue utilis\xE9e dans les visualisations de graphiques.","Couleur jaune utilis\xE9e dans les visualisations de graphiques.","Couleur orange utilis\xE9e dans les visualisations de graphiques.","Couleur verte utilis\xE9e dans les visualisations de graphiques.","Couleur violette utilis\xE9e dans les visualisations de graphiques.","Couleur d'arri\xE8re-plan de l'\xE9diteur.","Couleur de premier plan par d\xE9faut de l'\xE9diteur.","Couleur d\u2019arri\xE8re-plan du d\xE9filement \xE9pingl\xE9 dans l\u2019\xE9diteur","Couleur d\u2019arri\xE8re-plan du d\xE9filement \xE9pingl\xE9 lors du pointage pour l\u2019\xE9diteur","Couleur de bordure du d\xE9filement \xE9pingl\xE9 dans l\u2019\xE9diteur"," Couleur d\u2019ombre du d\xE9filement \xE9pingl\xE9 dans l\u2019\xE9diteur","Couleur d'arri\xE8re-plan des gadgets de l'\xE9diteur tels que rechercher/remplacer.","Couleur de premier plan des widgets de l'\xE9diteur, notamment Rechercher/remplacer.","Couleur de bordure des widgets de l'\xE9diteur. La couleur est utilis\xE9e uniquement si le widget choisit d'avoir une bordure et si la couleur n'est pas remplac\xE9e par un widget.","Couleur de bordure de la barre de redimensionnement des widgets de l'\xE9diteur. La couleur est utilis\xE9e uniquement si le widget choisit une bordure de redimensionnement et si la couleur n'est pas remplac\xE9e par un widget.","Couleur d'arri\xE8re-plan du texte d'erreur dans l'\xE9diteur. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les d\xE9corations sous-jacentes.","Couleur de premier plan de la ligne ondul\xE9e marquant les erreurs dans l'\xE9diteur.","Si cette option est d\xE9finie, couleur des doubles soulignements pour les erreurs dans l\u2019\xE9diteur.","Couleur d'arri\xE8re-plan du texte d'avertissement dans l'\xE9diteur. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les d\xE9corations sous-jacentes.","Couleur de premier plan de la ligne ondul\xE9e marquant les avertissements dans l'\xE9diteur.","Si cette option est d\xE9finie, couleur des doubles soulignements pour les avertissements dans l\u2019\xE9diteur.","Couleur d'arri\xE8re-plan du texte d'information dans l'\xE9diteur. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les d\xE9corations sous-jacentes.","Couleur de premier plan de la ligne ondul\xE9e marquant les informations dans l'\xE9diteur.","Si cette option est d\xE9finie, couleur des doubles soulignements pour les informations dans l\u2019\xE9diteur.","Couleur de premier plan de la ligne ondul\xE9e d'indication dans l'\xE9diteur.","Si cette option est d\xE9finie, couleur des doubles soulignements pour les conseils dans l\u2019\xE9diteur.","Couleur des liens actifs.","Couleur de la s\xE9lection de l'\xE9diteur.","Couleur du texte s\xE9lectionn\xE9 pour le contraste \xE9lev\xE9.","Couleur de la s\xE9lection dans un \xE9diteur inactif. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur des r\xE9gions dont le contenu est le m\xEAme que celui de la s\xE9lection. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur de bordure des r\xE9gions dont le contenu est identique \xE0 la s\xE9lection.","Couleur du r\xE9sultat de recherche actif.","Couleur du texte du r\xE9sultat de recherche actif.","Couleur des autres correspondances de recherche. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur de premier plan des autres r\xE9sultats de recherche.","Couleur de la plage limitant la recherche. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur de bordure du r\xE9sultat de recherche actif.","Couleur de bordure des autres r\xE9sultats de recherche.","Couleur de bordure de la plage limitant la recherche. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Surlignage sous le mot s\xE9lectionn\xE9 par pointage. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arri\xE8re-plan du pointage de l'\xE9diteur.","Couleur de premier plan du pointage de l'\xE9diteur.","Couleur de bordure du pointage de l'\xE9diteur.","Couleur d'arri\xE8re-plan de la barre d'\xE9tat du pointage de l'\xE9diteur.","Couleur de premier plan des indicateurs inline","Couleur d'arri\xE8re-plan des indicateurs inline","Couleur de premier plan des indicateurs inline pour les types","Couleur d'arri\xE8re-plan des indicateurs inline pour les types","Couleur de premier plan des indicateurs inline pour les param\xE8tres","Couleur d'arri\xE8re-plan des indicateurs inline pour les param\xE8tres","Couleur utilis\xE9e pour l'ic\xF4ne d'ampoule sugg\xE9rant des actions.","Couleur utilis\xE9e pour l'ic\xF4ne d'ampoule sugg\xE9rant des actions de correction automatique.","La couleur utilis\xE9e pour l\u2019ic\xF4ne AI de l\u2019ampoule.","Couleur d\u2019arri\xE8re-plan de mise en surbrillance d\u2019un extrait tabstop.","Couleur de bordure de mise en surbrillance d\u2019un extrait tabstop.","Couleur d\u2019arri\xE8re-plan de mise en surbrillance du tabstop final d\u2019un extrait.","Mettez en surbrillance la couleur de bordure du dernier taquet de tabulation d'un extrait de code.","Couleur d'arri\xE8re-plan du texte ins\xE9r\xE9. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arri\xE8re-plan du texte supprim\xE9. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arri\xE8re-plan des lignes ins\xE9r\xE9es. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arri\xE8re-plan des lignes supprim\xE9es. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur d\u2019arri\xE8re-plan de la marge o\xF9 les lignes ont \xE9t\xE9 ins\xE9r\xE9es","Couleur d\u2019arri\xE8re-plan de la marge o\xF9 les lignes ont \xE9t\xE9 supprim\xE9es","Premier plan de la r\xE8gle de vue d\u2019ensemble des diff\xE9rences pour le contenu ins\xE9r\xE9","Premier plan de la r\xE8gle de vue d\u2019ensemble des diff\xE9rences pour le contenu supprim\xE9","Couleur de contour du texte ins\xE9r\xE9.","Couleur de contour du texte supprim\xE9.","Couleur de bordure entre les deux \xE9diteurs de texte.","Couleur du remplissage diagonal de l'\xE9diteur de diff\xE9rences. Le remplissage diagonal est utilis\xE9 dans les vues de diff\xE9rences c\xF4te \xE0 c\xF4te.","Couleur d\u2019arri\xE8re-plan des blocs inchang\xE9s dans l\u2019\xE9diteur de diff\xE9rences.","Couleur de premier plan des blocs inchang\xE9s dans l\u2019\xE9diteur de diff\xE9rences.","Couleur d\u2019arri\xE8re-plan du code inchang\xE9 dans l\u2019\xE9diteur de diff\xE9rences.","Couleur de l'ombre des widgets, comme rechercher/remplacer, au sein de l'\xE9diteur.","Couleur de bordure des widgets, comme rechercher/remplacer au sein de l'\xE9diteur.","Arri\xE8re-plan de la barre d\u2019outils lors du survol des actions \xE0 l\u2019aide de la souris","Contour de la barre d\u2019outils lors du survol des actions \xE0 l\u2019aide de la souris","Arri\xE8re-plan de la barre d\u2019outils quand la souris est maintenue sur des actions","Couleur des \xE9l\xE9ments de navigation avec le focus.","Couleur de fond des \xE9l\xE9ments de navigation.","Couleur des \xE9l\xE9ments de navigation avec le focus.","Couleur des \xE9l\xE9ments de navigation s\xE9lectionn\xE9s.","Couleur de fond du s\xE9lecteur d\u2019\xE9l\xE9ment de navigation.","Arri\xE8re-plan d'en-t\xEAte actuel dans les conflits de fusion inline. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Arri\xE8re-plan de contenu actuel dans les conflits de fusion inline. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Arri\xE8re-plan d'en-t\xEAte entrant dans les conflits de fusion inline. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Arri\xE8re-plan de contenu entrant dans les conflits de fusion inline. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Arri\xE8re-plan d'en-t\xEAte de l'anc\xEAtre commun dans les conflits de fusion inline. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Arri\xE8re-plan de contenu de l'anc\xEAtre commun dans les conflits de fusion inline. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur de bordure des en-t\xEAtes et du s\xE9parateur dans les conflits de fusion inline.","Premier plan de la r\xE8gle d'aper\xE7u actuelle pour les conflits de fusion inline.","Premier plan de la r\xE8gle d'aper\xE7u entrante pour les conflits de fusion inline.","Arri\xE8re-plan de la r\xE8gle d'aper\xE7u de l'anc\xEAtre commun dans les conflits de fusion inline.","Couleur de marqueur de la r\xE8gle d'aper\xE7u pour rechercher les correspondances. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur de marqueur de la r\xE8gle d'aper\xE7u pour la mise en surbrillance des s\xE9lections. La couleur ne doit pas \xEAtre opaque pour ne pas masquer les ornements sous-jacents.","Couleur utilis\xE9e pour l'ic\xF4ne d'erreur des probl\xE8mes.","Couleur utilis\xE9e pour l'ic\xF4ne d'avertissement des probl\xE8mes.","Couleur utilis\xE9e pour l'ic\xF4ne d'informations des probl\xE8mes.","Arri\xE8re-plan de la zone d'entr\xE9e.","Premier plan de la zone d'entr\xE9e.","Bordure de la zone d'entr\xE9e.","Couleur de la bordure des options activ\xE9es dans les champs d'entr\xE9e.","Couleur d'arri\xE8re-plan des options activ\xE9es dans les champs d'entr\xE9e.","Couleur de pointage d\u2019arri\xE8re-plan des options dans les champs d\u2019entr\xE9e.","Couleur de premier plan des options activ\xE9es dans les champs d'entr\xE9e.","Couleur de premier plan de la zone d'entr\xE9e pour le texte d'espace r\xE9serv\xE9.","Couleur d'arri\xE8re-plan de la validation d'entr\xE9e pour la gravit\xE9 des informations.","Couleur de premier plan de validation de saisie pour la s\xE9v\xE9rit\xE9 Information.","Couleur de bordure de la validation d'entr\xE9e pour la gravit\xE9 des informations.","Couleur d'arri\xE8re-plan de la validation d'entr\xE9e pour la gravit\xE9 de l'avertissement.","Couleur de premier plan de la validation de la saisie pour la s\xE9v\xE9rit\xE9 Avertissement.","Couleur de bordure de la validation d'entr\xE9e pour la gravit\xE9 de l'avertissement.","Couleur d'arri\xE8re-plan de la validation d'entr\xE9e pour la gravit\xE9 de l'erreur.","Couleur de premier plan de la validation de saisie pour la s\xE9v\xE9rit\xE9 Erreur.","Couleur de bordure de la validation d'entr\xE9e pour la gravit\xE9 de l'erreur. ","Arri\xE8re-plan de la liste d\xE9roulante.","Arri\xE8re-plan de la liste d\xE9roulante.","Premier plan de la liste d\xE9roulante.","Bordure de la liste d\xE9roulante.","Couleur de premier plan du bouton.","Couleur du s\xE9parateur de boutons.","Couleur d'arri\xE8re-plan du bouton.","Couleur d'arri\xE8re-plan du bouton pendant le pointage.","Couleur de bordure du bouton.","Couleur de premier plan du bouton secondaire.","Couleur d'arri\xE8re-plan du bouton secondaire.","Couleur d'arri\xE8re-plan du bouton secondaire au moment du pointage.","Couleur de premier plan de l\u2019option radio active.","Couleur d\u2019arri\xE8re-plan de l\u2019option radio active.","Couleur de bordure de l\u2019option radio active.","Couleur de premier plan de l\u2019option radio inactive.","Couleur d\u2019arri\xE8re-plan de l\u2019option radio inactive.","Couleur de bordure de l\u2019option radio inactive.","Couleur d\u2019arri\xE8re-plan de l\u2019option radio active inactive lors du pointage.","Couleur de fond du widget Case \xE0 cocher.","Couleur d\u2019arri\xE8re-plan du widget de case \xE0 cocher lorsque l\u2019\xE9l\xE9ment dans lequel il se trouve est s\xE9lectionn\xE9.","Couleur de premier plan du widget Case \xE0 cocher.","Couleur de bordure du widget Case \xE0 cocher.","Couleur de bordure du widget de case \xE0 cocher lorsque l\u2019\xE9l\xE9ment dans lequel il se trouve est s\xE9lectionn\xE9.","Couleur d\u2019arri\xE8re-plan d\u2019\xE9tiquette de combinaison de touches. L\u2019\xE9tiquette est utilis\xE9e pour repr\xE9senter un raccourci clavier.","Couleur de premier plan d\u2019\xE9tiquette de combinaison de touches. L\u2019\xE9tiquette est utilis\xE9e pour repr\xE9senter un raccourci clavier.","Couleur de bordure de la combinaison de touches. L\u2019\xE9tiquette est utilis\xE9e pour repr\xE9senter un raccourci clavier.","Couleur de bordure du bas d\u2019\xE9tiquette de combinaison de touches. L\u2019\xE9tiquette est utilis\xE9e pour repr\xE9senter un raccourci clavier.","Couleur d'arri\xE8re-plan de la liste/l'arborescence pour l'\xE9l\xE9ment ayant le focus quand la liste/l'arborescence est active. Une liste/arborescence active peut \xEAtre s\xE9lectionn\xE9e au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de la liste/l'arborescence pour l'\xE9l\xE9ment ayant le focus quand la liste/l'arborescence est active. Une liste/arborescence active peut \xEAtre s\xE9lectionn\xE9e au clavier, elle ne l'est pas quand elle est inactive.","Couleur de contour de la liste/l'arborescence pour l'\xE9l\xE9ment ayant le focus quand la liste/l'arborescence est active. Une liste/arborescence active a le focus clavier, contrairement \xE0 une liste/arborescence inactive.","Couleur de contour de liste/arborescence pour l\u2019\xE9l\xE9ment cibl\xE9 lorsque la liste/l\u2019arborescence est active et s\xE9lectionn\xE9e. Une liste/arborescence active dispose d\u2019un focus clavier, ce qui n\u2019est pas le cas d\u2019une arborescence inactive.","Couleur d'arri\xE8re-plan de la liste/l'arborescence de l'\xE9l\xE9ment s\xE9lectionn\xE9 quand la liste/l'arborescence est active. Une liste/arborescence active peut \xEAtre s\xE9lectionn\xE9e au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de la liste/l'arborescence pour l'\xE9l\xE9ment s\xE9lectionn\xE9 quand la liste/l'arborescence est active. Une liste/arborescence active peut \xEAtre s\xE9lectionn\xE9e au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de l\u2019ic\xF4ne Liste/l'arborescence pour l'\xE9l\xE9ment s\xE9lectionn\xE9 quand la liste/l'arborescence est active. Une liste/arborescence active peut \xEAtre s\xE9lectionn\xE9e au clavier, elle ne l'est pas quand elle est inactive.","Couleur d'arri\xE8re-plan de la liste/l'arborescence pour l'\xE9l\xE9ment s\xE9lectionn\xE9 quand la liste/l'arborescence est inactive. Une liste/arborescence active peut \xEAtre s\xE9lectionn\xE9e au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de la liste/l'arborescence pour l'\xE9l\xE9ment s\xE9lectionn\xE9 quand la liste/l'arborescence est inactive. Une liste/arborescence active peut \xEAtre s\xE9lectionn\xE9e au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de l\u2019ic\xF4ne Liste/l'arborescence pour l'\xE9l\xE9ment s\xE9lectionn\xE9 quand la liste/l'arborescence est inactive. Une liste/arborescence active peut \xEAtre s\xE9lectionn\xE9e au clavier, elle ne l'est pas quand elle est inactive.","Couleur d'arri\xE8re-plan de la liste/l'arborescence pour l'\xE9l\xE9ment ayant le focus quand la liste/l'arborescence est active. Une liste/arborescence active peut \xEAtre s\xE9lectionn\xE9e au clavier (elle ne l'est pas quand elle est inactive).","Couleur de contour de la liste/l'arborescence pour l'\xE9l\xE9ment ayant le focus quand la liste/l'arborescence est inactive. Une liste/arborescence active a le focus clavier, contrairement \xE0 une liste/arborescence inactive.","Arri\xE8re-plan de la liste/l'arborescence pendant le pointage sur des \xE9l\xE9ments avec la souris.","Premier plan de la liste/l'arborescence pendant le pointage sur des \xE9l\xE9ments avec la souris.","Arri\xE8re-plan de l'op\xE9ration de glisser-d\xE9placer dans une liste/arborescence pendant le d\xE9placement d\u2019\xE9l\xE9ments sur d\u2019autres \xE9l\xE9ments avec la souris.","Couleur de bordure glisser-d\xE9placer de la liste/de l\u2019arborescence lors du d\xE9placement d\u2019\xE9l\xE9ments entre des \xE9l\xE9ments lors de l\u2019utilisation de la souris.","Couleur de premier plan dans la liste/l'arborescence pour la surbrillance des correspondances pendant la recherche dans une liste/arborescence.","Couleur de premier plan de la liste ou l\u2019arborescence pour la surbrillance des correspondances sur les \xE9l\xE9ments ayant le focus pendant la recherche dans une liste/arborescence.","Couleur de premier plan de liste/arbre pour les \xE9l\xE9ments non valides, par exemple une racine non r\xE9solue dans l\u2019Explorateur.","Couleur de premier plan des \xE9l\xE9ments de la liste contenant des erreurs.","Couleur de premier plan des \xE9l\xE9ments de liste contenant des avertissements.","Couleur d'arri\xE8re-plan du widget de filtre de type dans les listes et les arborescences.","Couleur de contour du widget de filtre de type dans les listes et les arborescences.","Couleur de contour du widget de filtre de type dans les listes et les arborescences, en l'absence de correspondance.","Appliquez une ombre \xE0 la couleur du widget filtre de type dans les listes et les arborescences.","Couleur d'arri\xE8re-plan de la correspondance filtr\xE9e.","Couleur de bordure de la correspondance filtr\xE9e.","Couleur de premier plan de la liste/l\u2019arborescence des \xE9l\xE9ments att\xE9nu\xE9s.","Couleur de trait de l'arborescence pour les rep\xE8res de mise en retrait.","Couleur de trait d\u2019arborescence pour les rep\xE8res de mise en retrait qui ne sont pas actifs.","Couleur de la bordure du tableau entre les colonnes.","Couleur d'arri\xE8re-plan pour les lignes de tableau impaires.","Couleur d'arri\xE8re-plan de la liste d'actions.","Couleur de premier plan de la liste d'actions.","Couleur de premier plan de la liste d\u2019actions pour l\u2019\xE9l\xE9ment focalis\xE9.","Couleur d'arri\xE8re-plan de la liste d'actions pour l'\xE9l\xE9ment focalis\xE9.","Couleur de bordure des menus.","Couleur de premier plan des \xE9l\xE9ments de menu.","Couleur d'arri\xE8re-plan des \xE9l\xE9ments de menu.","Couleur de premier plan de l'\xE9l\xE9ment de menu s\xE9lectionn\xE9 dans les menus.","Couleur d'arri\xE8re-plan de l'\xE9l\xE9ment de menu s\xE9lectionn\xE9 dans les menus.","Couleur de bordure de l'\xE9l\xE9ment de menu s\xE9lectionn\xE9 dans les menus.","Couleur d'un \xE9l\xE9ment de menu s\xE9parateur dans les menus.","Couleur de marqueur de la minimap pour les correspondances.","Couleur de marqueur minimap pour les s\xE9lections r\xE9p\xE9t\xE9es de l\u2019\xE9diteur.","Couleur de marqueur du minimap pour la s\xE9lection de l'\xE9diteur.","Couleur de marqueur de minimap pour les informations.","Couleur de marqueur de minimap pour les avertissements.","Couleur de marqueur de minimap pour les erreurs.","Couleur d'arri\xE8re-plan du minimap.","Opacit\xE9 des \xE9l\xE9ments de premier plan rendus dans la minimap. Par exemple, \xAB #000000c0 \xBB affiche les \xE9l\xE9ments avec une opacit\xE9 de 75 %.","Couleur d'arri\xE8re-plan du curseur de minimap.","Couleur d'arri\xE8re-plan du curseur de minimap pendant le survol.","Couleur d'arri\xE8re-plan du curseur de minimap pendant un clic.","Couleur de bordure des fen\xEAtres coulissantes.","Couleur de fond des badges. Les badges sont de courts libell\xE9s d'information, ex. le nombre de r\xE9sultats de recherche.","Couleur des badges. Les badges sont de courts libell\xE9s d'information, ex. le nombre de r\xE9sultats de recherche.","Ombre de la barre de d\xE9filement pour indiquer que la vue d\xE9file.","Couleur de fond du curseur de la barre de d\xE9filement.","Couleur de fond du curseur de la barre de d\xE9filement lors du survol.","Couleur d\u2019arri\xE8re-plan de la barre de d\xE9filement lorsqu'on clique dessus.","Couleur de fond pour la barre de progression qui peut s'afficher lors d'op\xE9rations longues.","Couleur d'arri\xE8re-plan du s\xE9lecteur rapide. Le widget de s\xE9lecteur rapide est le conteneur de s\xE9lecteurs comme la palette de commandes.","Couleur de premier plan du s\xE9lecteur rapide. Le widget de s\xE9lecteur rapide est le conteneur de s\xE9lecteurs comme la palette de commandes.","Couleur d'arri\xE8re-plan du titre du s\xE9lecteur rapide. Le widget de s\xE9lecteur rapide est le conteneur de s\xE9lecteurs comme la palette de commandes.","Couleur du s\xE9lecteur rapide pour les \xE9tiquettes de regroupement.","Couleur du s\xE9lecteur rapide pour les bordures de regroupement.","Utilisez quickInputList.focusBackground \xE0 la place","Couleur de premier plan du s\xE9lecteur rapide pour l\u2019\xE9l\xE9ment ayant le focus.","Couleur de premier plan de l\u2019ic\xF4ne du s\xE9lecteur rapide pour l\u2019\xE9l\xE9ment ayant le focus.","Couleur d'arri\xE8re-plan du s\xE9lecteur rapide pour l'\xE9l\xE9ment ayant le focus.","Couleur du texte dans le message d\u2019ach\xE8vement de la viewlet de recherche.","Couleur des correspondances de requ\xEAte de l'\xE9diteur de recherche.","Couleur de bordure des correspondances de requ\xEAte de l'\xE9diteur de recherche.","Cette couleur doit \xEAtre transparente ou son contenu sera masqu\xE9","Utiliser la couleur par d\xE9faut.","ID de la police \xE0 utiliser. Si aucune valeur n'est d\xE9finie, la police d\xE9finie en premier est utilis\xE9e.","Caract\xE8re de police associ\xE9 \xE0 la d\xE9finition d'ic\xF4ne.","Ic\xF4ne de l'action de fermeture dans les widgets.","Ic\xF4ne d'acc\xE8s \xE0 l'emplacement pr\xE9c\xE9dent de l'\xE9diteur.","Ic\xF4ne d'acc\xE8s \xE0 l'emplacement suivant de l'\xE9diteur.","Les fichiers suivants ont \xE9t\xE9 ferm\xE9s et modifi\xE9s sur le disque\xA0: {0}.","Les fichiers suivants ont \xE9t\xE9 modifi\xE9s de mani\xE8re incompatible : {0}.","Impossible d'annuler '{0}' dans tous les fichiers. {1}","Impossible d'annuler '{0}' dans tous les fichiers. {1}","Impossible d'annuler '{0}' dans tous les fichiers, car des modifications ont \xE9t\xE9 apport\xE9es \xE0 {1}","Impossible d'annuler '{0}' dans tous les fichiers, car une op\xE9ration d'annulation ou de r\xE9tablissement est d\xE9j\xE0 en cours d'ex\xE9cution sur {1}","Impossible d'annuler '{0}' dans tous les fichiers, car une op\xE9ration d'annulation ou de r\xE9tablissement s'est produite dans l'intervalle","Souhaitez-vous annuler '{0}' dans tous les fichiers\xA0?","&&Annuler dans {0} fichiers","Annuler ce &&fichier","Impossible d'annuler '{0}', car une op\xE9ration d'annulation ou de r\xE9tablissement est d\xE9j\xE0 en cours d'ex\xE9cution.","Voulez-vous annuler '{0}'\xA0?","&&Oui","Non","Impossible de r\xE9p\xE9ter '{0}' dans tous les fichiers. {1}","Impossible de r\xE9p\xE9ter '{0}' dans tous les fichiers. {1}","Impossible de r\xE9p\xE9ter '{0}' dans tous les fichiers, car des modifications ont \xE9t\xE9 apport\xE9es \xE0 {1}","Impossible de r\xE9tablir '{0}' dans tous les fichiers, car une op\xE9ration d'annulation ou de r\xE9tablissement est d\xE9j\xE0 en cours d'ex\xE9cution pour {1}","Impossible de r\xE9tablir '{0}' dans tous les fichiers, car une op\xE9ration d'annulation ou de r\xE9tablissement s'est produite dans l'intervalle","Impossible de r\xE9tablir '{0}', car une op\xE9ration d'annulation ou de r\xE9tablissement est d\xE9j\xE0 en cours d'ex\xE9cution.","Espace de travail de code"],globalThis._VSCODE_NLS_LANGUAGE="fr";

//# sourceMappingURL=../min-maps/nls.messages.fr.js.map
});
