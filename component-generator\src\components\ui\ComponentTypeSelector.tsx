'use client';

import React from 'react';
import { Navigation, Square, CreditCard, FileText, Zap, Layout } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ComponentType } from '@/types';

interface ComponentTypeSelectorProps {
  value: ComponentType;
  onChange: (type: ComponentType) => void;
  className?: string;
}

const componentTypes: {
  value: ComponentType;
  label: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
}[] = [
  {
    value: 'navbar',
    label: 'Navigation Bar',
    description: 'Header navigation with logo and menu items',
    icon: Navigation,
    color: 'text-blue-500',
  },
  {
    value: 'button',
    label: 'Button',
    description: 'Interactive button with various styles',
    icon: Square,
    color: 'text-green-500',
  },
  {
    value: 'card',
    label: 'Card',
    description: 'Content container with optional image and actions',
    icon: CreditCard,
    color: 'text-purple-500',
  },
  {
    value: 'form',
    label: 'Form',
    description: 'Input form with validation and styling',
    icon: FileText,
    color: 'text-orange-500',
  },
  {
    value: 'hero',
    label: 'Hero Section',
    description: 'Landing page hero with call-to-action',
    icon: Zap,
    color: 'text-red-500',
  },
  {
    value: 'footer',
    label: 'Footer',
    description: 'Page footer with links and information',
    icon: Layout,
    color: 'text-indigo-500',
  },
];

export default function ComponentTypeSelector({ value, onChange, className }: ComponentTypeSelectorProps) {
  return (
    <div className={cn('space-y-3', className)}>
      <h3 className="text-lg font-semibold text-foreground">Choose Component Type</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        {componentTypes.map((type) => {
          const Icon = type.icon;
          const isSelected = value === type.value;
          
          return (
            <button
              key={type.value}
              onClick={() => onChange(type.value)}
              className={cn(
                'flex flex-col items-start p-4 text-left border rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
                isSelected
                  ? 'border-primary bg-primary/10 shadow-md'
                  : 'border-border bg-card hover:bg-accent hover:border-accent-foreground/20'
              )}
            >
              <div className="flex items-center space-x-3 mb-2">
                <Icon className={cn('w-5 h-5', isSelected ? 'text-primary' : type.color)} />
                <span className={cn(
                  'font-medium',
                  isSelected ? 'text-primary' : 'text-foreground'
                )}>
                  {type.label}
                </span>
              </div>
              
              <p className={cn(
                'text-sm',
                isSelected ? 'text-primary/80' : 'text-muted-foreground'
              )}>
                {type.description}
              </p>
              
              {isSelected && (
                <div className="mt-2 px-2 py-1 bg-primary/20 text-primary text-xs rounded-full">
                  Selected
                </div>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
}
