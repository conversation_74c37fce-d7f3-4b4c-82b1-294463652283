'use client';

import React from 'react';
import { useComponentStore } from '@/store/componentStore';
import { ButtonConfig } from '@/types';
import ColorPicker from '@/components/ui/ColorPicker';
import SizeSelector from '@/components/ui/SizeSelector';

export default function ButtonGenerator() {
  const { config, updateConfig } = useComponentStore();
  const buttonConfig = config as ButtonConfig;

  const updateButtonConfig = (updates: Partial<ButtonConfig>) => {
    updateConfig(updates);
  };

  return (
    <div className="space-y-6">
      {/* Size Selection */}
      <SizeSelector
        value={buttonConfig.size}
        onChange={(size) => updateButtonConfig({ size })}
        label="Size"
      />

      {/* Colors */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-foreground">Colors</h4>
        <div className="grid grid-cols-1 gap-4">
          <ColorPicker
            color={buttonConfig.colors.primary}
            onChange={(color) => updateButtonConfig({
              colors: { ...buttonConfig.colors, primary: color }
            })}
            label="Primary Color"
          />
          <ColorPicker
            color={buttonConfig.colors.text}
            onChange={(color) => updateButtonConfig({
              colors: { ...buttonConfig.colors, text: color }
            })}
            label="Text Color"
          />
          <ColorPicker
            color={buttonConfig.colors.background}
            onChange={(color) => updateButtonConfig({
              colors: { ...buttonConfig.colors, background: color }
            })}
            label="Background Color"
          />
        </div>
      </div>

      {/* Button Content */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-foreground">Content</h4>
        
        <div className="space-y-2">
          <label className="block text-sm text-muted-foreground">Button Text</label>
          <input
            type="text"
            value={buttonConfig.text}
            onChange={(e) => updateButtonConfig({ text: e.target.value })}
            className="w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
            placeholder="Enter button text"
          />
        </div>
      </div>

      {/* Button Variant */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-foreground">Variant</h4>
        
        <div className="grid grid-cols-2 gap-2">
          {['primary', 'secondary', 'outline', 'ghost', 'destructive'].map((variant) => (
            <button
              key={variant}
              type="button"
              onClick={() => updateButtonConfig({ variant: variant as any })}
              className={`px-3 py-2 text-sm border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-ring ${
                buttonConfig.variant === variant
                  ? 'border-primary bg-primary/10 text-primary'
                  : 'border-border bg-background text-muted-foreground hover:bg-accent hover:text-accent-foreground'
              }`}
            >
              {variant.charAt(0).toUpperCase() + variant.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Icon Configuration */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-foreground">Icon</h4>
        
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="icon-enabled"
            checked={buttonConfig.icon.enabled}
            onChange={(e) => updateButtonConfig({
              icon: { ...buttonConfig.icon, enabled: e.target.checked }
            })}
            className="rounded border-border text-primary focus:ring-ring"
          />
          <label htmlFor="icon-enabled" className="text-sm text-muted-foreground">
            Enable Icon
          </label>
        </div>

        {buttonConfig.icon.enabled && (
          <div className="space-y-2">
            <div>
              <label className="block text-sm text-muted-foreground">Icon Position</label>
              <select
                value={buttonConfig.icon.position}
                onChange={(e) => updateButtonConfig({
                  icon: { ...buttonConfig.icon, position: e.target.value as any }
                })}
                className="w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="left">Left</option>
                <option value="right">Right</option>
              </select>
            </div>

            <div>
              <label className="block text-sm text-muted-foreground">Icon Name</label>
              <select
                value={buttonConfig.icon.name}
                onChange={(e) => updateButtonConfig({
                  icon: { ...buttonConfig.icon, name: e.target.value }
                })}
                className="w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="arrow-right">Arrow Right</option>
                <option value="arrow-left">Arrow Left</option>
                <option value="plus">Plus</option>
                <option value="download">Download</option>
                <option value="external-link">External Link</option>
                <option value="check">Check</option>
                <option value="x">X</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Button Options */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-foreground">Options</h4>
        
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="full-width"
              checked={buttonConfig.fullWidth}
              onChange={(e) => updateButtonConfig({ fullWidth: e.target.checked })}
              className="rounded border-border text-primary focus:ring-ring"
            />
            <label htmlFor="full-width" className="text-sm text-muted-foreground">
              Full Width
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="disabled"
              checked={buttonConfig.disabled}
              onChange={(e) => updateButtonConfig({ disabled: e.target.checked })}
              className="rounded border-border text-primary focus:ring-ring"
            />
            <label htmlFor="disabled" className="text-sm text-muted-foreground">
              Disabled State
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="loading"
              checked={buttonConfig.loading}
              onChange={(e) => updateButtonConfig({ loading: e.target.checked })}
              className="rounded border-border text-primary focus:ring-ring"
            />
            <label htmlFor="loading" className="text-sm text-muted-foreground">
              Loading State
            </label>
          </div>
        </div>
      </div>

      {/* Style Options */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-foreground">Style</h4>
        
        <div className="space-y-2">
          <label className="block text-sm text-muted-foreground">Border Radius</label>
          <select
            value={buttonConfig.rounded}
            onChange={(e) => updateButtonConfig({ rounded: e.target.value as any })}
            className="w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
          >
            <option value="none">None</option>
            <option value="sm">Small</option>
            <option value="md">Medium</option>
            <option value="lg">Large</option>
            <option value="xl">Extra Large</option>
            <option value="full">Full (Pill)</option>
          </select>
        </div>

        <div className="space-y-2">
          <label className="block text-sm text-muted-foreground">Shadow</label>
          <select
            value={buttonConfig.shadow}
            onChange={(e) => updateButtonConfig({ shadow: e.target.value as any })}
            className="w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
          >
            <option value="none">None</option>
            <option value="sm">Small</option>
            <option value="md">Medium</option>
            <option value="lg">Large</option>
            <option value="xl">Extra Large</option>
          </select>
        </div>
      </div>
    </div>
  );
}
