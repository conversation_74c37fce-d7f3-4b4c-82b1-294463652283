'use client';

import React from 'react';
import { Monitor, Tablet, Smartphone } from 'lucide-react';
import { cn } from '@/lib/utils';

interface PreviewContainerProps {
  children: React.ReactNode;
  mode: 'desktop' | 'tablet' | 'mobile';
  onModeChange: (mode: 'desktop' | 'tablet' | 'mobile') => void;
  className?: string;
}

const previewModes = [
  { value: 'desktop' as const, label: 'Desktop', icon: Monitor, width: 'w-full' },
  { value: 'tablet' as const, label: 'Tablet', icon: Tablet, width: 'w-[768px]' },
  { value: 'mobile' as const, label: 'Mobile', icon: Smartphone, width: 'w-[375px]' },
];

export default function PreviewContainer({ children, mode, onModeChange, className }: PreviewContainerProps) {
  return (
    <div className={cn('space-y-4', className)}>
      {/* Preview mode selector */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-foreground">Live Preview</h3>
        
        <div className="flex items-center space-x-1 bg-muted p-1 rounded-lg">
          {previewModes.map((previewMode) => {
            const Icon = previewMode.icon;
            const isActive = mode === previewMode.value;
            
            return (
              <button
                key={previewMode.value}
                onClick={() => onModeChange(previewMode.value)}
                className={cn(
                  'flex items-center space-x-2 px-3 py-2 text-sm rounded-md transition-colors',
                  isActive
                    ? 'bg-background text-foreground shadow-sm'
                    : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                )}
                title={`Preview in ${previewMode.label} mode`}
              >
                <Icon className="w-4 h-4" />
                <span className="hidden sm:inline">{previewMode.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Preview container */}
      <div className="bg-muted/30 border border-border rounded-lg p-6 overflow-x-auto">
        <div className="flex justify-center">
          <div
            className={cn(
              'transition-all duration-300 bg-background border border-border rounded-lg shadow-sm overflow-hidden',
              previewModes.find(m => m.value === mode)?.width
            )}
          >
            <div className="min-h-[200px] p-4">
              {children}
            </div>
          </div>
        </div>
      </div>

      {/* Preview info */}
      <div className="flex items-center justify-center text-sm text-muted-foreground">
        <span>
          Viewing in {mode} mode
          {mode === 'tablet' && ' (768px)'}
          {mode === 'mobile' && ' (375px)'}
        </span>
      </div>
    </div>
  );
}
