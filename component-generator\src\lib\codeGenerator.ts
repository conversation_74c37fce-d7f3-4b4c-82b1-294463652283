import { ComponentConfig, NavbarConfig, ButtonConfig, CardConfig, FormConfig } from '@/types';
import { getRoundedClass, getShadowClass, generateCSSVariables } from './utils';

// Base template for React components
const getComponentTemplate = (
  componentName: string,
  imports: string[],
  props: string,
  jsx: string,
  styles?: string
): string => {
  const importsSection = imports.length > 0 ? imports.join('\n') + '\n\n' : '';
  const stylesSection = styles ? `\nconst styles = \`\n${styles}\`;\n\n` : '';
  
  return `${importsSection}${stylesSection}interface ${componentName}Props {
${props}
}

export default function ${componentName}({ ${props.split('\n').map(line => {
    const match = line.trim().match(/(\w+):/);
    return match ? match[1] : '';
  }).filter(Boolean).join(', ')} }: ${componentName}Props) {
  return (
${jsx}
  );
}`;
};

// Generate Navbar component
export function generateNavbarCode(config: NavbarConfig): string {
  const { colors, size, rounded, shadow, position, transparent, logo, navigation, cta } = config;
  
  const baseClasses = [
    'w-full',
    position === 'fixed' ? 'fixed top-0 z-50' : position === 'sticky' ? 'sticky top-0 z-40' : '',
    transparent ? 'bg-transparent' : 'bg-background',
    !transparent ? getShadowClass(shadow) : '',
    getRoundedClass(rounded),
  ].filter(Boolean).join(' ');

  const containerClasses = [
    'container mx-auto px-4',
    size === 'sm' ? 'py-2' : size === 'lg' ? 'py-6' : 'py-4',
  ].join(' ');

  const logoClasses = [
    'text-xl font-bold text-foreground',
    size === 'sm' ? 'text-lg' : size === 'lg' ? 'text-2xl' : 'text-xl',
  ].join(' ');

  const navItemClasses = [
    'text-muted-foreground hover:text-foreground transition-colors',
    size === 'sm' ? 'text-sm' : size === 'lg' ? 'text-lg' : 'text-base',
  ].join(' ');

  const ctaClasses = [
    'px-4 py-2 rounded-md font-medium transition-colors',
    cta.variant === 'primary' ? 'bg-primary text-primary-foreground hover:bg-primary/90' :
    cta.variant === 'secondary' ? 'bg-secondary text-secondary-foreground hover:bg-secondary/90' :
    'border border-border text-foreground hover:bg-accent',
    size === 'sm' ? 'px-3 py-1 text-sm' : size === 'lg' ? 'px-6 py-3 text-lg' : '',
  ].filter(Boolean).join(' ');

  const navItems = navigation.items.map(item => 
    `            <a
              href="${item.href}"
              className="${navItemClasses}${item.active ? ' text-foreground font-medium' : ''}"
            >
              ${item.label}
            </a>`
  ).join('\n');

  const jsx = `    <nav className="${baseClasses}">
      <div className="${containerClasses}">
        <div className="flex items-center justify-between">
          <div className="${logoClasses}">
            ${logo.text}
          </div>
          
          <div className="hidden md:flex items-center space-x-8">
${navItems}
          </div>
          
          ${cta.enabled ? `<button className="${ctaClasses}">
            ${cta.text}
          </button>` : ''}
        </div>
      </div>
    </nav>`;

  const props = `  className?: string;`;
  
  const styles = generateCSSVariables(colors);

  return getComponentTemplate('Navbar', [], props, jsx, styles);
}

// Generate Button component
export function generateButtonCode(config: ButtonConfig): string {
  const { colors, size, rounded, shadow, variant, text, icon, fullWidth, disabled, loading } = config;
  
  const baseClasses = [
    'inline-flex items-center justify-center font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50',
    fullWidth ? 'w-full' : '',
    getRoundedClass(rounded),
    getShadowClass(shadow),
  ].filter(Boolean);

  const sizeClasses = {
    sm: 'h-8 px-3 text-sm',
    md: 'h-10 px-4 py-2',
    lg: 'h-12 px-6 text-lg',
    xl: 'h-14 px-8 text-xl',
  };

  const variantClasses = {
    primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/90',
    outline: 'border border-border bg-background hover:bg-accent hover:text-accent-foreground',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
  };

  const buttonClasses = [
    ...baseClasses,
    sizeClasses[size],
    variantClasses[variant],
  ].join(' ');

  const iconElement = icon.enabled ? (
    icon.position === 'left' 
      ? `        <ChevronRight className="mr-2 h-4 w-4" />`
      : `        <ChevronRight className="ml-2 h-4 w-4" />`
  ) : '';

  const jsx = `    <button
      className="${buttonClasses}"
      disabled={disabled || loading}
      {...props}
    >
      ${loading ? `      <Loader2 className="mr-2 h-4 w-4 animate-spin" />` : ''}
      ${icon.enabled && icon.position === 'left' ? iconElement : ''}
      {children || "${text}"}
      ${icon.enabled && icon.position === 'right' ? iconElement : ''}
    </button>`;

  const imports = icon.enabled || loading ? [
    `import { ChevronRight${loading ? ', Loader2' : ''} } from 'lucide-react';`
  ] : [];

  const props = `  children?: React.ReactNode;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  onClick?: () => void;`;

  const styles = generateCSSVariables(colors);

  return getComponentTemplate('Button', imports, props, jsx, styles);
}

// Main code generator function
export function generateComponentCode(config: ComponentConfig): string {
  switch (config.type) {
    case 'navbar':
      return generateNavbarCode(config as NavbarConfig);
    case 'button':
      return generateButtonCode(config as ButtonConfig);
    case 'card':
      // TODO: Implement card generator
      return '// Card generator coming soon...';
    case 'form':
      // TODO: Implement form generator
      return '// Form generator coming soon...';
    default:
      return '// Component type not supported yet';
  }
}
