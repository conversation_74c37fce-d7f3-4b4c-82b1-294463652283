'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { SizeOption } from '@/types';

interface SizeSelectorProps {
  value: SizeOption;
  onChange: (size: SizeOption) => void;
  label?: string;
  className?: string;
}

const sizeOptions: { value: SizeOption; label: string; description: string }[] = [
  { value: 'sm', label: 'Small', description: 'Compact size' },
  { value: 'md', label: 'Medium', description: 'Default size' },
  { value: 'lg', label: 'Large', description: 'Spacious size' },
  { value: 'xl', label: 'Extra Large', description: 'Maximum size' },
];

export default function SizeSelector({ value, onChange, label, className }: SizeSelectorProps) {
  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <label className="block text-sm font-medium text-foreground">
          {label}
        </label>
      )}
      
      <div className="grid grid-cols-2 gap-2">
        {sizeOptions.map((option) => (
          <button
            key={option.value}
            type="button"
            onClick={() => onChange(option.value)}
            className={cn(
              'flex flex-col items-center justify-center p-3 text-sm border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
              value === option.value
                ? 'border-primary bg-primary/10 text-primary'
                : 'border-border bg-background text-muted-foreground hover:bg-accent hover:text-accent-foreground'
            )}
          >
            <span className="font-medium">{option.label}</span>
            <span className="text-xs opacity-70">{option.description}</span>
          </button>
        ))}
      </div>
    </div>
  );
}
