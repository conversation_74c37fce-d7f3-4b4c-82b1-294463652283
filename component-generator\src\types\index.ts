// Component types
export type ComponentType = 'navbar' | 'button' | 'card' | 'form' | 'hero' | 'footer';

// Size options
export type SizeOption = 'sm' | 'md' | 'lg' | 'xl';

// Color configuration
export interface ColorConfig {
  primary: string;
  secondary: string;
  background: string;
  text: string;
  accent: string;
}

// Component configuration base
export interface BaseComponentConfig {
  type: ComponentType;
  size: SizeOption;
  colors: ColorConfig;
  rounded: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full';
  shadow: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  font: string;
}

// Navbar specific configuration
export interface NavbarConfig extends BaseComponentConfig {
  type: 'navbar';
  layout: 'horizontal' | 'vertical';
  position: 'static' | 'fixed' | 'sticky';
  transparent: boolean;
  logo: {
    text: string;
    position: 'left' | 'center' | 'right';
  };
  navigation: {
    items: Array<{
      label: string;
      href: string;
      active?: boolean;
    }>;
    alignment: 'left' | 'center' | 'right' | 'space-between';
  };
  cta: {
    enabled: boolean;
    text: string;
    variant: 'primary' | 'secondary' | 'outline';
  };
}

// Button specific configuration
export interface ButtonConfig extends BaseComponentConfig {
  type: 'button';
  variant: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  text: string;
  icon: {
    enabled: boolean;
    position: 'left' | 'right';
    name: string;
  };
  fullWidth: boolean;
  disabled: boolean;
  loading: boolean;
}

// Card specific configuration
export interface CardConfig extends BaseComponentConfig {
  type: 'card';
  layout: 'simple' | 'with-image' | 'with-header' | 'with-footer';
  content: {
    title: string;
    description: string;
    image?: {
      src: string;
      alt: string;
      position: 'top' | 'left' | 'right';
    };
    actions: Array<{
      label: string;
      variant: 'primary' | 'secondary' | 'outline';
    }>;
  };
  hover: {
    enabled: boolean;
    effect: 'lift' | 'glow' | 'scale';
  };
}

// Form specific configuration
export interface FormConfig extends BaseComponentConfig {
  type: 'form';
  layout: 'vertical' | 'horizontal' | 'grid';
  fields: Array<{
    type: 'text' | 'email' | 'password' | 'textarea' | 'select' | 'checkbox' | 'radio';
    label: string;
    placeholder: string;
    required: boolean;
    validation?: string;
  }>;
  submitButton: {
    text: string;
    variant: 'primary' | 'secondary';
    fullWidth: boolean;
  };
}

// Union type for all component configurations
export type ComponentConfig = NavbarConfig | ButtonConfig | CardConfig | FormConfig;

// Store state interface
export interface ComponentGeneratorState {
  currentComponent: ComponentType;
  config: ComponentConfig;
  generatedCode: string;
  previewMode: 'desktop' | 'tablet' | 'mobile';
  darkMode: boolean;
  
  // Actions
  setCurrentComponent: (type: ComponentType) => void;
  updateConfig: (config: Partial<ComponentConfig>) => void;
  setGeneratedCode: (code: string) => void;
  setPreviewMode: (mode: 'desktop' | 'tablet' | 'mobile') => void;
  toggleDarkMode: () => void;
  resetConfig: () => void;
}

// Preset interface
export interface ComponentPreset {
  id: string;
  name: string;
  description: string;
  config: ComponentConfig;
  createdAt: Date;
  tags: string[];
}
