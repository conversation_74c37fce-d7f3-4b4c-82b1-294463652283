import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Convert hex color to CSS custom property
export function hexToHsl(hex: string): string {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  if (!result) return '0 0% 0%';

  const r = parseInt(result[1], 16) / 255;
  const g = parseInt(result[2], 16) / 255;
  const b = parseInt(result[3], 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max === min) {
    h = s = 0; // achromatic
  } else {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0);
        break;
      case g:
        h = (b - r) / d + 2;
        break;
      case b:
        h = (r - g) / d + 4;
        break;
    }
    h /= 6;
  }

  return `${Math.round(h * 360)} ${Math.round(s * 100)}% ${Math.round(l * 100)}%`;
}

// Generate CSS custom properties from color config
export function generateCSSVariables(colors: Record<string, string>): string {
  return Object.entries(colors)
    .map(([key, value]) => `  --${key}: ${hexToHsl(value)};`)
    .join('\n');
}

// Format generated code with Prettier (simplified version)
export function formatCode(code: string): string {
  // Basic formatting - in a real app, you'd use Prettier API
  return code
    .replace(/\s+/g, ' ')
    .replace(/>\s+</g, '><')
    .replace(/{\s+/g, '{ ')
    .replace(/\s+}/g, ' }')
    .trim();
}

// Copy text to clipboard
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
      document.execCommand('copy');
      document.body.removeChild(textArea);
      return true;
    } catch (fallbackErr) {
      document.body.removeChild(textArea);
      return false;
    }
  }
}

// Download text as file
export function downloadFile(content: string, filename: string, contentType = 'text/plain'): void {
  const blob = new Blob([content], { type: contentType });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}

// Generate responsive classes based on size
export function getResponsiveClasses(size: string, property: string): string {
  const sizeMap = {
    sm: { base: '2', md: '3', lg: '4' },
    md: { base: '4', md: '6', lg: '8' },
    lg: { base: '6', md: '8', lg: '12' },
    xl: { base: '8', md: '12', lg: '16' },
  };

  const sizes = sizeMap[size as keyof typeof sizeMap] || sizeMap.md;
  return `${property}-${sizes.base} md:${property}-${sizes.md} lg:${property}-${sizes.lg}`;
}

// Generate shadow classes
export function getShadowClass(shadow: string): string {
  const shadowMap = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
    xl: 'shadow-xl',
  };
  return shadowMap[shadow as keyof typeof shadowMap] || '';
}

// Generate rounded classes
export function getRoundedClass(rounded: string): string {
  const roundedMap = {
    none: '',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl',
    full: 'rounded-full',
  };
  return roundedMap[rounded as keyof typeof roundedMap] || '';
}

// Debounce function for performance
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
