'use client';

import React, { useState, useEffect } from 'react';
import { Save, Folder, Trash2, Download, Upload } from 'lucide-react';
import { useComponentStore } from '@/store/componentStore';
import { ComponentPreset } from '@/types';
import { cn } from '@/lib/utils';

interface PresetManagerProps {
  className?: string;
}

export default function PresetManager({ className }: PresetManagerProps) {
  const { config, updateConfig } = useComponentStore();
  const [presets, setPresets] = useState<ComponentPreset[]>([]);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [presetName, setPresetName] = useState('');
  const [presetDescription, setPresetDescription] = useState('');

  // Load presets from localStorage on mount
  useEffect(() => {
    const savedPresets = localStorage.getItem('component-generator-presets');
    if (savedPresets) {
      try {
        setPresets(JSON.parse(savedPresets));
      } catch (error) {
        console.error('Failed to load presets:', error);
      }
    }
  }, []);

  // Save presets to localStorage whenever presets change
  useEffect(() => {
    localStorage.setItem('component-generator-presets', JSON.stringify(presets));
  }, [presets]);

  const savePreset = () => {
    if (!presetName.trim()) return;

    const newPreset: ComponentPreset = {
      id: Date.now().toString(),
      name: presetName.trim(),
      description: presetDescription.trim(),
      config,
      createdAt: new Date(),
      tags: [config.type],
    };

    setPresets(prev => [newPreset, ...prev]);
    setPresetName('');
    setPresetDescription('');
    setShowSaveDialog(false);
  };

  const loadPreset = (preset: ComponentPreset) => {
    updateConfig(preset.config);
  };

  const deletePreset = (presetId: string) => {
    setPresets(prev => prev.filter(p => p.id !== presetId));
  };

  const exportPresets = () => {
    const dataStr = JSON.stringify(presets, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'component-generator-presets.json';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const importPresets = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedPresets = JSON.parse(e.target?.result as string);
        if (Array.isArray(importedPresets)) {
          setPresets(prev => [...importedPresets, ...prev]);
        }
      } catch (error) {
        console.error('Failed to import presets:', error);
        alert('Failed to import presets. Please check the file format.');
      }
    };
    reader.readAsText(file);
    event.target.value = ''; // Reset input
  };

  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-foreground">Presets</h3>
        
        <div className="flex items-center space-x-2">
          <input
            type="file"
            accept=".json"
            onChange={importPresets}
            className="hidden"
            id="import-presets"
          />
          <label
            htmlFor="import-presets"
            className="flex items-center space-x-1 px-2 py-1 text-xs bg-background border border-border rounded hover:bg-accent hover:text-accent-foreground transition-colors cursor-pointer"
            title="Import presets"
          >
            <Upload className="w-3 h-3" />
            <span>Import</span>
          </label>
          
          <button
            onClick={exportPresets}
            className="flex items-center space-x-1 px-2 py-1 text-xs bg-background border border-border rounded hover:bg-accent hover:text-accent-foreground transition-colors"
            title="Export presets"
            disabled={presets.length === 0}
          >
            <Download className="w-3 h-3" />
            <span>Export</span>
          </button>
          
          <button
            onClick={() => setShowSaveDialog(true)}
            className="flex items-center space-x-1 px-2 py-1 text-xs bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors"
          >
            <Save className="w-3 h-3" />
            <span>Save</span>
          </button>
        </div>
      </div>

      {/* Save Dialog */}
      {showSaveDialog && (
        <div className="p-4 bg-card border border-border rounded-lg space-y-3">
          <h4 className="font-medium text-foreground">Save Current Configuration</h4>
          
          <div className="space-y-2">
            <input
              type="text"
              placeholder="Preset name"
              value={presetName}
              onChange={(e) => setPresetName(e.target.value)}
              className="w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
            />
            
            <textarea
              placeholder="Description (optional)"
              value={presetDescription}
              onChange={(e) => setPresetDescription(e.target.value)}
              rows={2}
              className="w-full px-3 py-2 text-sm border border-border rounded-md bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring resize-none"
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={savePreset}
              disabled={!presetName.trim()}
              className="px-3 py-1 text-sm bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Save
            </button>
            <button
              onClick={() => {
                setShowSaveDialog(false);
                setPresetName('');
                setPresetDescription('');
              }}
              className="px-3 py-1 text-sm bg-background border border-border rounded hover:bg-accent hover:text-accent-foreground transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Presets List */}
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {presets.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground border-2 border-dashed border-border rounded-lg">
            <Folder className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No presets saved yet</p>
            <p className="text-xs">Save your current configuration to create a preset</p>
          </div>
        ) : (
          presets.map((preset) => (
            <div
              key={preset.id}
              className="flex items-center justify-between p-3 bg-card border border-border rounded-lg hover:bg-accent/50 transition-colors"
            >
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <h4 className="font-medium text-foreground truncate">{preset.name}</h4>
                  <span className="px-2 py-1 text-xs bg-primary/10 text-primary rounded-full">
                    {preset.config.type}
                  </span>
                </div>
                {preset.description && (
                  <p className="text-sm text-muted-foreground truncate mt-1">
                    {preset.description}
                  </p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  {new Date(preset.createdAt).toLocaleDateString()}
                </p>
              </div>
              
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => loadPreset(preset)}
                  className="p-1 text-muted-foreground hover:text-foreground transition-colors"
                  title="Load preset"
                >
                  <Folder className="w-4 h-4" />
                </button>
                <button
                  onClick={() => deletePreset(preset.id)}
                  className="p-1 text-muted-foreground hover:text-destructive transition-colors"
                  title="Delete preset"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
